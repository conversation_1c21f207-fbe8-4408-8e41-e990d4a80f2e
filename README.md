# Oluchy - Sistema de Gestão de Estoque e Vendas

![Versão](https://img.shields.io/badge/versão-1.0.0-blue)

## Sobre o Projeto

Oluchy é um sistema completo de gestão de estoque e vendas desenvolvido para facilitar o controle de inventário, gerenciamento de vendas e acompanhamento de fluxo de caixa. Com uma interface moderna e responsiva, o sistema oferece uma solução abrangente para empresas que precisam gerenciar seus produtos, vendas e finanças de forma eficiente.

O sistema foi projetado com foco na usabilidade, oferecendo uma experiência intuitiva tanto em dispositivos desktop quanto móveis, permitindo que os usuários acessem informações importantes e realizem operações de qualquer lugar.

## Tecnologias Utilizadas

O projeto foi desenvolvido utilizando um conjunto moderno de tecnologias:

### Frontend
- **React 18** - Biblioteca JavaScript para construção de interfaces
- **TypeScript** - Superset tipado de JavaScript
- **Vite** - Ferramenta de build rápida para desenvolvimento
- **Tailwind CSS** - Framework CSS utilitário
- **shadcn/ui** - Componentes de UI reutilizáveis e acessíveis
- **React Router** - Roteamento para aplicações React
- **React Query** - Gerenciamento de estado e requisições
- **Recharts** - Biblioteca para criação de gráficos
- **React Hook Form** - Gerenciamento de formulários
- **Zod** - Validação de esquemas
- **Lucide React** - Ícones modernos e consistentes

### Backend
- **Supabase** - Plataforma de backend como serviço
  - Banco de dados PostgreSQL
  - Autenticação e autorização
  - Armazenamento de arquivos
  - Funções serverless
  - Realtime subscriptions

## Requisitos do Sistema

Para executar o projeto localmente, você precisará ter instalado:

- **Node.js** (versão 18.x ou superior)
- **npm** (versão 9.x ou superior) ou **yarn** (versão 1.22.x ou superior)
- Navegador moderno (Chrome, Firefox, Edge, Safari)
- Conexão com a internet (para acessar o Supabase)

## Instalação e Configuração

Siga os passos abaixo para configurar o projeto em seu ambiente local:

### 1. Clone o repositório

```bash
git clone <URL_DO_REPOSITÓRIO>
cd oluchy
```

### 2. Instale as dependências

```bash
npm install
# ou
yarn install
```

### 3. Configure o Supabase

1. Crie uma conta no [Supabase](https://supabase.com/) caso ainda não tenha
2. Crie um novo projeto no Supabase
3. Copie o arquivo `.env.example` para `.env.local`:
   ```bash
   cp .env.example .env.local
   ```
4. Preencha as variáveis de ambiente no arquivo `.env.local` com suas credenciais do Supabase:
   ```
   VITE_SUPABASE_URL=sua_url_do_supabase
   VITE_SUPABASE_ANON_KEY=sua_chave_anon_do_supabase
   ```

### 4. Inicie o servidor de desenvolvimento

```bash
npm run dev
# ou
yarn dev
```

O aplicativo estará disponível em `http://localhost:8080`.

## Estrutura do Projeto

```
oluchy/
├── public/                  # Arquivos estáticos
├── src/                     # Código fonte
│   ├── components/          # Componentes reutilizáveis
│   │   ├── ui/              # Componentes de UI (shadcn)
│   │   ├── dashboard/       # Componentes do dashboard
│   │   ├── inventory/       # Componentes do inventário
│   │   ├── sales/           # Componentes de vendas
│   │   └── ...
│   ├── context/             # Contextos React (AuthContext, SidebarContext)
│   ├── hooks/               # Hooks personalizados
│   ├── integrations/        # Integrações com serviços externos
│   │   └── supabase/        # Cliente e tipos do Supabase
│   ├── lib/                 # Utilitários e funções auxiliares
│   ├── pages/               # Páginas principais da aplicação
│   ├── services/            # Serviços para comunicação com o backend
│   ├── types/               # Definições de tipos TypeScript
│   ├── App.tsx              # Componente principal da aplicação
│   ├── index.css            # Estilos globais
│   └── main.tsx             # Ponto de entrada da aplicação
├── .env.example             # Exemplo de variáveis de ambiente
├── .gitignore               # Arquivos ignorados pelo Git
├── components.json          # Configuração do shadcn/ui
├── index.html               # Arquivo HTML principal
├── package.json             # Dependências e scripts
├── tailwind.config.ts       # Configuração do Tailwind CSS
├── tsconfig.json            # Configuração do TypeScript
└── vite.config.ts           # Configuração do Vite
```

## Funcionalidades Principais

### Dashboard
- Visão geral do negócio com métricas importantes
- Gráficos de vendas diárias e semanais
- Alertas de produtos com estoque baixo
- Comparativo de desempenho com períodos anteriores

### Vendas
- Registro de novas vendas
- Acompanhamento de status de vendas (Realizado, Reservado, Cancelado, etc.)
- Filtros por período, status e vendedor
- Upload de comprovantes de pagamento
- Associação com entregadores

### Estoque
- Cadastro e gerenciamento de produtos
- Controle de variações (cores, tamanhos)
- Alertas de estoque baixo
- Categorização de produtos
- Histórico de movimentações

### Fluxo de Caixa
- Registro de entradas e saídas
- Relatórios por período
- Filtros por método de pagamento
- Balanço financeiro

### Usuários
- Gerenciamento de usuários do sistema
- Níveis de acesso (administrador e usuário comum)
- Perfis de vendedores

### Configurações
- Personalização do sistema
- Gerenciamento de categorias, cores e tamanhos
- Configuração de métodos de pagamento

## Guia de Contribuição

Se você deseja contribuir com o projeto, siga estas diretrizes:

1. Faça um fork do repositório
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Faça commit das suas alterações (`git commit -m 'Adiciona nova funcionalidade'`)
4. Faça push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

### Boas Práticas
- Siga o padrão de código existente
- Escreva testes para novas funcionalidades
- Documente seu código
- Mantenha o arquivo `actualizacoes.md` atualizado com suas alterações

## Funcionalidades Futuras

O projeto Oluchy tem um roadmap ambicioso para expansão e melhoria contínua. Algumas das funcionalidades planejadas incluem:

### Transformação em SaaS
- Evolução para um modelo SaaS (Software as a Service) para atender diversos nichos
- Suporte tanto para empresas de produtos quanto de serviços
- Personalização por segmento de mercado

### Planos de Assinatura
- **Plano Pleno**: Funcionalidades básicas para pequenos negócios
- **Plano Pro**: Recursos avançados para empresas em crescimento
- **Plano Max**: Solução completa com todas as funcionalidades para grandes operações

### Sistema de Pagamentos
- Integração com métodos de pagamento do mercado angolano
- Suporte para transferências bancárias
- Integração com Multicaixa Express
- Gestão de assinaturas e faturamento

### Painel de Super Administrador
- Interface dedicada para gerenciamento de empresas assinantes
- Métricas e análises de uso da plataforma
- Gestão de planos e cobranças

### Melhorias Técnicas
- Implementação de PWA (Progressive Web App) para melhor experiência mobile
- Sistema de tradução para múltiplos idiomas
- Integração com Inteligência Artificial para análises preditivas e recomendações
- API pública para integração com outros sistemas

## Licença

Este projeto está licenciado sob a [MIT License](LICENSE).

## Contato

Carlos Cesar - [GitHub](https://github.com/doublec560)

---

Desenvolvido com ❤️ usando React, TypeScript e Supabase.