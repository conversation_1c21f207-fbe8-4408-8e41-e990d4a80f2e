# SalesFlow Demo - Sistema de Gestão de Vendas

![Versão](https://img.shields.io/badge/versão-demo-blue) ![Status](https://img.shields.io/badge/status-portfolio-green)

## 🎯 Sobre o Projeto

O SalesFlow Demo é um sistema completo de gestão de vendas e estoque desenvolvida com React, TypeScript e shadcn/ui. Esta é uma **versão de demonstração** criada para portfolio, que utiliza armazenamento local para simular todas as funcionalidades de um sistema real.

O sistema permite gestão completa de produtos, vendas, usuários, fluxo de caixa e relatórios, oferecendo uma experiência completa de um sistema de gestão empresarial moderno.

## 🚀 Tecnologias Utilizadas

### Frontend
- **React 18** - Biblioteca para interfaces de usuário
- **TypeScript** - Linguagem de programação com tipagem estática
- **Vite** - Ferramenta de build rápida e moderna
- **Tailwind CSS** - Framework CSS utility-first
- **shadcn/ui** - Biblioteca de componentes acessíveis e customizáveis
- **React Router DOM** - Roteamento para aplicações React
- **React Hook Form** - Gerenciamento de formulários
- **React Query** - Gerenciamento de estado e cache
- **Recharts** - Biblioteca para gráficos e visualizações
- **Lucide React** - Ícones modernos
- **Sonner** - Sistema de notificações toast

### Versão de Produção (Não incluída nesta demo)
- **Supabase** - Backend as a Service (BaaS)
- **PostgreSQL** - Banco de dados relacional
- **Row Level Security (RLS)** - Segurança a nível de linha
- **Edge Functions** - Funções serverless
- **Real-time subscriptions** - Atualizações em tempo real

## 🔧 Como Testar

### Pré-requisitos
- Node.js 18+
- npm ou yarn

### Instalação e Execução

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd salesflow-demo
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Inicie o servidor de desenvolvimento:**
```bash
npm run dev
```

4. **Acesse a aplicação:**
   - Abra [http://localhost:8000](http://localhost:5173) no seu navegador

### Credenciais de Teste

A aplicação possui duas contas de teste pré-configuradas:

#### 👨‍💼 Administrador
- **Email:** <EMAIL>
- **Senha:** 123
- **Permissões:** Acesso completo a todas as funcionalidades

#### 👤 Vendedor
- **Email:** <EMAIL>
- **Senha:** 123
- **Permissões:** Acesso limitado (vendas, produtos, dashboard)

## 📱 Funcionalidades Disponíveis

### Para Administradores:
- ✅ Dashboard com métricas completas
- ✅ Gestão completa de produtos e variações
- ✅ Controle total de vendas
- ✅ Gestão de usuários e permissões
- ✅ Relatórios financeiros
- ✅ Configurações do sistema
- ✅ Fluxo de caixa completo

### Para Vendedores:
- ✅ Dashboard com métricas básicas
- ✅ Visualização de produtos
- ✅ Registro de vendas
- ✅ Controle de estoque básico

## 🗂️ Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes shadcn/ui
│   ├── dashboard/      # Componentes do dashboard
│   ├── reports/        # Componentes de relatórios
│   └── ...
├── context/            # Contextos React (Auth, Sidebar)
├── hooks/              # Hooks customizados
├── lib/                # Utilitários e configurações
│   ├── localStorage.ts # Sistema de armazenamento local
│   └── localClient.ts  # Cliente que simula Supabase
├── pages/              # Páginas da aplicação
├── services/           # Serviços de API (adaptados para localStorage)
├── types/              # Definições de tipos TypeScript
└── integrations/       # Integrações (adaptadas para demo)
```

## 🔒 Segurança e Dados

### Versão Demo (Atual)
- ✅ **Dados Fictícios**: Todos os dados são fictícios e gerados automaticamente
- ✅ **Armazenamento Local**: Dados salvos apenas no navegador do usuário
- ✅ **Sem Persistência**: Dados são perdidos ao limpar o cache do navegador
- ✅ **Sem Conexão Externa**: Nenhuma comunicação com servidores externos

### Versão de Produção
- 🔐 **Autenticação JWT**: Sistema de autenticação seguro
- 🔐 **Row Level Security**: Políticas de segurança a nível de banco
- 🔐 **Criptografia**: Dados sensíveis criptografados
- 🔐 **Backup Automático**: Backups regulares dos dados
- 🔐 **Auditoria**: Log completo de todas as ações

## 📊 Scripts Disponíveis

- `npm run dev` - Inicia servidor de desenvolvimento
- `npm run build` - Gera build de produção
- `npm run preview` - Visualiza build de produção
- `npm run lint` - Executa linting do código

## 🎨 Características Técnicas

- **Responsive Design**: Interface adaptável para desktop, tablet e mobile
- **Dark/Light Mode**: Alternância entre temas claro e escuro
- **Acessibilidade**: Componentes seguem padrões WCAG
- **Performance**: Otimizado com lazy loading e code splitting
- **TypeScript**: 100% tipado para maior segurança
- **Componentização**: Arquitetura modular e reutilizável

## 📝 Notas Importantes

### ⚠️ Esta é uma Versão de Demonstração

- **Propósito**: Criada exclusivamente para demonstração de habilidades técnicas
- **Dados**: Todos os dados são fictícios e não representam informações reais
- **Limitações**: Algumas funcionalidades são simuladas (ex: pagamentos)
- **Armazenamento**: Utiliza localStorage, dados não persistem entre dispositivos

### 🚀 Versão de Produção

A versão real deste sistema utiliza:
- Backend robusto com Supabase
- Banco de dados PostgreSQL
- Autenticação e autorização avançadas
- Integrações com APIs de pagamento
- Sistema de backup e recuperação
- Monitoramento e logs detalhados

## 📞 Contato

Este projeto foi desenvolvido como parte do meu portfolio. Para mais informações sobre a implementação completa ou outras tecnologias, entre em contato.

---

**Desenvolvido com ❤️ para demonstração de habilidades em desenvolvimento full-stack**