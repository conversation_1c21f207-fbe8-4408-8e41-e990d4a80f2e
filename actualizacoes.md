# Registro de Atualizações

Este arquivo documenta todas as atualizações feitas no sistema.



## 2025-04-20 (19:10)

### Correção da Exibição de Quantidade no Gráfico de Vendas

**Descrição:** Corrigida a exibição do tooltip no gráfico de vendas do período para mostrar "Qt" em vez de "Valor" para a série de quantidade de vendas.

**Detalhes técnicos:**
1. Modificado o formatador do tooltip no gráfico de vendas do período para usar formatadores diferentes dependendo do tipo de dado
2. Implementada lógica condicional que verifica o nome da série ("Vendas") para aplicar o rótulo "Qt" em vez de "Valor"
3. Mantida a formatação monetária para a série de valores

**Arquivos modificados:**
- `src/pages/Reports.tsx`

**Benefícios:**
- Maior clareza na interpretação dos dados no gráfico
- Consistência com a nomenclatura usada em outras partes da aplicação
- Evita confusão entre valores monetários e quantidades

## 2025-04-20 (18:45)

### Melhoria no Espaçamento dos Títulos no PDF de Relatórios

**Descrição:** Adicionado espaçamento adequado antes dos títulos das seções no PDF de relatórios para melhorar a legibilidade e a aparência visual.

**Detalhes técnicos:**
1. Adicionado espaço vertical (10 unidades) antes de cada título de seção no PDF
2. Aplicada a mudança em todas as seções: Vendas por Vendedor, Entregas por Entregador, Vendas por Método de Pagamento e Produtos Mais Vendidos
3. Mantido o espaçamento consistente em todas as seções

**Arquivos modificados:**
- `src/pages/Reports.tsx`

**Benefícios:**
- Melhor separação visual entre as seções do relatório
- Maior legibilidade dos títulos
- Aparência mais profissional do documento PDF
- Melhor hierarquia visual da informação


## 2025-04-20 (18:15)

### Melhorias na Página de Relatórios e Exportação para PDF

**Descrição:** Implementadas melhorias visuais e funcionais na página de relatórios e na exportação para PDF, incluindo gráficos de pizza, mini-gráficos (sparklines), ícones mais descritivos e um layout de PDF mais elaborado.

**Detalhes técnicos:**
1. Adicionados gráficos de pizza para visualização da distribuição de vendas por método de pagamento
2. Implementados mini-gráficos (sparklines) nos cards de métricas para mostrar tendências
3. Melhorados os ícones existentes com versões mais descritivas e adicionados ícones para cada tipo de relatório nas abas
4. Adicionado sistema de cores temáticas para diferentes tipos de dados (vendas, entregas, produtos)
5. Criado cabeçalho mais elaborado para o PDF com logo placeholder e informações da empresa
6. Melhorado o layout do PDF com seções visualmente distintas e cores temáticas
7. Adicionada numeração de páginas no rodapé do PDF
8. Criados novos componentes: `PaymentPieChart` e `SparklineChart`

**Arquivos modificados:**
- `src/pages/Reports.tsx`
- `src/components/reports/MetricCard.tsx`
- `src/components/reports/PaymentPieChart.tsx` (novo)
- `src/components/reports/SparklineChart.tsx` (novo)

**Benefícios:**
- Visualização de dados mais rica e informativa
- Melhor experiência do usuário com elementos visuais mais descritivos
- Relatórios em PDF mais profissionais e organizados
- Identificação mais rápida de tendências e padrões nos dados

## 2025-04-20 (17:31)

### Remoção do Botão de Fechar nas Notificações

**Descrição:** Removido o botão de fechar (X) das notificações para melhorar a aparência visual e simplificar a interface.

**Detalhes técnicos:**
1. Modificado o componente `Toaster` para desativar o botão de fechar, definindo a propriedade `closeButton` como `false`

**Arquivos modificados:**
- `src/components/ui/sonner.tsx`

**Benefícios:**
- Interface mais limpa e menos poluída visualmente
- Experiência de usuário mais consistente com o design mostrado
- Foco na mensagem da notificação sem elementos de interface adicionais

## 2025-04-20 (17:30)

### Redesign do Sistema de Notificações com Barra de Progresso Animada

**Descrição:** Implementado um novo design para o sistema de notificações com borda colorida à esquerda, ícones distintos e uma barra de progresso animada que indica o tempo restante da notificação.

**Detalhes técnicos:**
1. Redesenhado o componente `Toaster` para seguir um novo padrão visual:
   - Notificações com borda colorida à esquerda em vez de fundo colorido completo
   - Adicionados ícones específicos para cada tipo de notificação (CheckCircle2, XCircle, AlertTriangle, Info)
   - Implementada barra de progresso animada na parte inferior que diminui gradualmente até a notificação desaparecer
2. Criada animação personalizada 'progress' no Tailwind para a barra de progresso
3. Ajustada a duração das notificações para 4 segundos para melhor visualização da animação
4. Melhorada a estrutura visual com títulos em negrito e descrições em texto menor
5. Adicionada documentação JSDoc atualizada ao componente Toaster

**Arquivos modificados:**
- `src/components/ui/sonner.tsx`
- `tailwind.config.ts`
- `src/components/NotificationTest.tsx`

**Benefícios:**
- Design mais moderno e elegante para as notificações
- Feedback visual do tempo restante através da barra de progresso
- Melhor identificação do tipo de notificação através de cores e ícones
- Experiência de usuário mais intuitiva e agradável
- Maior acessibilidade com melhor contraste e elementos visuais distintos

## 2025-04-20 (17:07)

### Correção dos Tooltips no Controle de Acesso do Inventário

**Descrição:** Corrigida a implementação dos tooltips informativos nos botões desabilitados do controle de acesso no inventário.

**Detalhes técnicos:**
1. Adicionado `delayDuration={300}` aos componentes `TooltipProvider` para melhorar a experiência do usuário
2. Adicionado atributo `side` aos componentes `TooltipContent` para controlar a posição dos tooltips
3. Envolvido os botões desabilitados em elementos `<span>` para garantir que os tooltips funcionem corretamente
4. Removida importação não utilizada de `DialogFooter` para melhorar a qualidade do código

**Arquivos modificados:**
- `src/components/InventoryTable.tsx`

**Benefícios:**
- Melhor experiência do usuário com tooltips que funcionam corretamente
- Feedback visual mais claro sobre as permissões do usuário
- Código mais limpo e organizado

## 2025-04-20 (17:03)

### Implementação de Controle de Acesso Baseado em Capacidades no Inventário

**Descrição:** Implementado um sistema de controle de acesso baseado em capacidades específicas no componente de tabela de inventário, seguindo o princípio do privilégio mínimo.

**Detalhes técnicos:**
1. Adicionadas novas props ao componente InventoryTable para controlar permissões:
   - `canAddProducts`: Controla se o usuário pode adicionar novos produtos
   - `canEditProducts`: Controla se o usuário pode editar produtos existentes
   - `canDeleteProducts`: Controla se o usuário pode excluir produtos
2. Implementada lógica condicional para mostrar/ocultar botões com base nas permissões
3. Adicionados tooltips informativos nos botões desabilitados explicando por que a ação não está disponível
4. Atualizada a página Inventory.tsx para determinar as permissões do usuário com base em seu papel (role)
5. Configurado para que apenas administradores tenham permissões completas de gerenciamento de produtos

**Arquivos modificados:**
- `src/components/InventoryTable.tsx`
- `src/pages/Inventory.tsx`

**Benefícios:**
- Maior segurança com controle granular de permissões
- Implementação do princípio do privilégio mínimo
- Melhor experiência de usuário com feedback visual sobre permissões
- Prevenção de ações não autorizadas
- Base para futuras implementações de controle de acesso em outros componentes

## 2025-04-20 (16:00)

### Adição de Documentação ao Código

**Descrição:** Adicionada documentação detalhada ao código para melhorar a compreensão e manutenção do projeto.

**Detalhes técnicos:**
1. Adicionados comentários JSDoc para componentes, hooks e contextos principais
2. Documentados parâmetros, retornos e exemplos de uso para funções e hooks
3. Adicionadas descrições detalhadas para interfaces e tipos
4. Criada função utilitária `getUpdateTimestamp` para gerar timestamps consistentes
5. Explicados fluxos de dados e lógica de negócio complexa
6. Documentados componentes de relatórios com descrições detalhadas e exemplos de uso
7. Adicionados comentários explicativos para blocos de código complexos
8. Documentados componentes principais como Navbar e Sidebar
9. Documentadas páginas principais como Dashboard e Inventory

**Arquivos modificados:**
- `src/App.tsx`
- `src/context/AuthContext.tsx`
- `src/context/SidebarContext.tsx`
- `src/hooks/use-dashboard.tsx`
- `src/lib/getUpdateTimestamp.ts` (novo)
- `src/components/reports/StatusOverview.tsx`
- `src/pages/Reports.tsx`
- `src/components/Navbar.tsx`
- `src/components/Sidebar.tsx`
- `src/pages/Dashboard.tsx`
- `src/pages/Inventory.tsx`
- `src/pages/Login.tsx`
- `src/pages/Settings.tsx`
- `src/pages/Sales.tsx`
- `src/pages/Users.tsx`
- `src/components/SalesTable.tsx`
- `src/components/InventoryTable.tsx`
- `src/pages/CashFlow.tsx`
- `src/pages/NotFound.tsx`
- `src/components/users/EmptyState.tsx`

**Benefícios:**
- Melhor compreensão do código para novos desenvolvedores
- Facilitação da manutenção e evolução do sistema
- Padronização da documentação em todo o projeto
- Melhor integração com IDEs para autocompletar e dicas de tipos
- Documentação de fluxos complexos e decisões de arquitetura
- Melhor entendimento dos componentes de relatórios e suas funcionalidades

## 2025-04-20 (15:00)

### Padronização do Layout nas Páginas de Vendas, Estoque e Usuários

**Descrição:** Reorganizado o layout das páginas de vendas, estoque e usuários para melhorar a usabilidade em dispositivos móveis e desktop, seguindo um padrão consistente em todas as páginas.

**Detalhes técnicos:**
1. Reorganizada a estrutura dos elementos no cabeçalho das tabelas de vendas, estoque e usuários
2. Implementados layouts distintos para dispositivos móveis e desktop em todas as páginas
3. Em dispositivos móveis:
   - Organizados os elementos em coluna na ordem: campo de busca, filtros, botão de ação principal
   - Esticados todos os elementos para ocupar a largura total da tela
   - Adicionado espaçamento adequado entre os elementos
   - Padronizada a altura dos botões (h-10) para melhor aparência visual
4. Em desktop:
   - Movidos os controles (busca, filtros, botão) para a mesma linha do seletor de registros
   - Mantido o seletor de registros à esquerda e os controles à direita
   - Alinhada a altura dos elementos para melhor aparência visual

**Arquivos modificados:**
- `src/components/SalesTable.tsx`
- `src/components/InventoryTable.tsx`
- `src/components/UsersTable.tsx`

**Benefícios:**
- Melhor organização visual dos elementos de interface
- Layout otimizado para diferentes tamanhos de tela
- Maior consistência entre as diferentes páginas do sistema
- Melhor aproveitamento do espaço horizontal em desktop
- Experiência de usuário mais intuitiva em dispositivos móveis


## 2025-04-20 (14:27)

### Melhoria na Disposição de Elementos na Página de Fluxo de Caixa em Dispositivos Móveis

**Descrição:** Corrigida a posição do campo de busca e do seletor de registros por página na página de Fluxo de Caixa em dispositivos móveis.

**Detalhes técnicos:**
1. Invertida a ordem dos elementos em dispositivos móveis usando flex-col-reverse
2. Adicionado espaçamento inferior (mb-4) ao campo de busca em dispositivos móveis
3. Removido o espaçamento em dispositivos desktop (sm:mb-0)

**Arquivos modificados:**
- `src/pages/CashFlow.tsx`

**Benefícios:**
- Melhor experiência de usuário em dispositivos móveis
- Campo de busca mais acessível e visível no topo da seção
- Melhor hierarquia visual dos elementos de interface

## 2025-04-20 (14:23)

### Melhorias na Interface de Usuário dos Cards e Menu

**Descrição:** Realizadas melhorias na interface de usuário para tornar os textos e ícones mais precisos e intuitivos.

**Detalhes técnicos:**
1. Modificado o texto de comparação no card "Vendas de Hoje" de "vs. semana anterior" para "vs. dia anterior" para refletir corretamente a comparação feita
2. Substituído o ícone DollarSign pelo ícone Banknote (nota de dinheiro) no card "Vendas de Hoje" e no menu de Fluxo de Caixa para melhor representar o contexto

**Arquivos modificados:**
- `src/components/DashboardCard.tsx`
- `src/components/dashboard/DashboardCards.tsx`
- `src/components/Sidebar.tsx`

**Benefícios:**
- Maior precisão nas informações apresentadas ao usuário
- Melhor representação visual com ícones mais adequados ao contexto
- Experiência de usuário mais intuitiva e coerente

## 2025-04-20 (14:11)

### Melhoria na Exibição de Gráficos com Tradução e Formatação de Números

**Descrição:** Aprimorada a exibição dos gráficos no dashboard com tradução de labels e formatação de números seguindo o padrão português.

**Detalhes técnicos:**
1. Traduzida a label "value" para "Valor" nos gráficos de vendas
2. Traduzida a label "value" para "Qt" nos gráficos de estoque
3. Implementada formatação de números com espaçamento adequado (ex: 55 000,00) nos eixos Y e tooltips
4. Adicionada lógica para determinar automaticamente qual label usar com base no título do gráfico
5. Corrigido problema de corte dos valores no gráfico de vendas aumentando a margem esquerda
6. Removidas casas decimais para o gráfico de estoque (quantidade) para melhor visualização

**Arquivos modificados:**
- `src/components/DashboardChart.tsx`

**Benefícios:**
- Melhor experiência para usuários de língua portuguesa
- Maior clareza na interpretação dos dados nos gráficos
- Consistência visual com o restante da aplicação
- Melhor legibilidade dos valores numéricos com separação adequada de milhares

## 2025-04-20 (14:02)

### Adição de Tooltip no Botão de Alertas de Estoque

**Descrição:** Adicionado um tooltip ao botão de seta nos alertas de estoque para melhorar a usabilidade e fornecer informações contextuais ao usuário.

**Detalhes técnicos:**
1. Importados os componentes de tooltip do sistema de UI
2. Adicionado um tooltip ao botão de seta no componente StockAlertsCard
3. Implementada uma propriedade opcional para personalizar o texto do tooltip
4. Configurado o texto do tooltip como "Ir para estoque"

**Arquivos modificados:**
- `src/components/dashboard/StockAlertsCard.tsx`
- `src/components/dashboard/DashboardCharts.tsx`

**Benefícios:**
- Melhor usabilidade com informações contextuais sobre a ação do botão
- Experiência de usuário mais intuitiva
- Maior clareza sobre a navegação do sistema

## 2025-04-20 (13:52)

### Adição de Ícones de Aviso nos Alertas de Estoque

**Descrição:** Adicionados ícones de aviso (AlertTriangle) nos alertas de estoque para melhorar a visualização da criticidade do estoque baixo.

**Detalhes técnicos:**
1. Importado o ícone AlertTriangle do Lucide React
2. Adicionado o ícone ao lado da quantidade de estoque em cada item da lista
3. Implementada coloração diferenciada para o ícone baseada na criticidade do estoque:
   - Vermelho para itens com quantidade <= 2
   - Laranja para os demais itens com estoque baixo

**Arquivos modificados:**
- `src/components/dashboard/StockAlertsCard.tsx`

**Benefícios:**
- Melhor visualização da criticidade do estoque
- Identificação mais rápida de itens em situação crítica
- Experiência visual mais intuitiva para o usuário

## 2025-04-20 (13:13)

### Melhoria na Posição do Botão de Toggle do Sidebar

**Descrição:** Reposicionado o botão de toggle do sidebar para melhorar a experiência do usuário e a estética da interface.

**Detalhes técnicos:**
1. Removido o botão de toggle do topo do sidebar
2. Adicionado o botão de toggle no header (Navbar), próximo ao logo
3. Ajustado o espaçamento e a aparência do botão para melhor integração com o header

**Arquivos modificados:**
- `src/components/Sidebar.tsx`
- `src/components/Navbar.tsx`

**Benefícios:**
- Melhor estética da interface
- Posição mais intuitiva seguindo padrões de UI/UX modernos
- Eliminação de espaço vazio desnecessário no sidebar
- Melhor agrupamento de controles relacionados à navegação

## 2025-04-20 (11:15)

### Correção do Ajuste Automático do Conteúdo Principal

**Descrição:** Corrigido o comportamento do conteúdo principal para se ajustar automaticamente quando o sidebar é expandido ou colapsado.

**Detalhes técnicos:**
1. Implementado um contexto centralizado para gerenciar o estado do sidebar
2. Modificado o componente MainContent para usar margin-left em vez de padding-left
3. Adicionada transição suave para o conteúdo principal se ajustar ao tamanho do sidebar
4. Centralizado o conteúdo com container e padding adequados

**Arquivos modificados:**
- `src/context/SidebarContext.tsx`
- `src/components/Sidebar.tsx`
- `src/App.tsx`

**Benefícios:**
- Melhor experiência de usuário com transições suaves
- Conteúdo principal se ajusta automaticamente ao estado do sidebar
- Gerenciamento de estado centralizado para melhor manutenção

## 2025-04-20 (12:55)

### Melhorias na Navegação do Sidebar

**Descrição:** Implementadas melhorias na barra lateral para melhorar a experiência do usuário em dispositivos móveis e desktop.

**Detalhes técnicos:**
1. Adicionado fechamento automático do sidebar após seleção de página em dispositivos móveis
2. Implementado modo compacto para o sidebar em desktop, permitindo visualizar apenas ícones
3. Adicionado botão de toggle para alternar entre modo expandido e compacto
4. Criado sistema de tooltips para exibir os nomes das páginas quando o sidebar está no modo compacto
5. Implementada persistência da preferência do usuário usando localStorage

**Arquivos modificados:**
- `src/components/Sidebar.tsx`
- `src/App.tsx`
- `src/context/SidebarContext.tsx` (novo)

**Benefícios:**
- Melhor experiência em dispositivos móveis com fechamento automático após navegação
- Mais espaço útil na tela em desktop quando o sidebar está colapsado
- Persistência das preferências do usuário entre sessões
- Interface mais limpa e organizada

## 2025-04-20 (12:15)

### Padronização da Formatação de Valores Monetários

**Descrição:** Padronizado o formato de exibição de valores monetários em toda a aplicação para manter consistência visual.

**Detalhes técnicos:**
1. Modificados os componentes que exibem valores monetários para usar a função `formatCurrency` do utils.ts
2. Ajustado o espaçamento e formatação dos valores para seguir o padrão "11 467 000,00 Kz"
3. Atualizado o tamanho da fonte para manter consistência visual entre os cards

**Arquivos modificados:**
- `src/pages/Sales.tsx`
- `src/components/SalesTable.tsx`
- `src/components/InventoryTable.tsx`

**Benefícios:**
- Consistência visual em toda a aplicação
- Melhor legibilidade dos valores monetários
- Experiência de usuário mais coesa

## 2025-04-20 (11:56)

### Implementação de Indicadores de Campos Obrigatórios

**Descrição:** Adicionado um sistema consistente para marcar campos obrigatórios em todos os formulários da aplicação.

**Detalhes técnicos:**
1. Criado um novo componente `RequiredFieldIndicator` que renderiza um asterisco vermelho usando a cor destrutiva do tema
2. Atualizado o componente `FormLabel` para aceitar uma propriedade `required` que exibe o indicador
3. Modificados os formulários existentes para usar a nova propriedade `required` no FormLabel
4. Adicionada uma nota explicativa no final dos formulários explicando que os campos marcados com um asterisco vermelho são obrigatórios

**Arquivos modificados:**
- `src/components/ui/required-field-indicator.tsx` (novo)
- `src/components/ui/form.tsx`
- `src/components/NewSaleForm.tsx`
- `src/components/EditSaleForm.tsx`
- `src/components/NewProductForm.tsx`
- `src/components/NewUserForm.tsx`
- `src/components/EditUserForm.tsx`
- `src/components/ProfileEditModal.tsx`
- `src/pages/CashFlow.tsx`

**Benefícios:**
- Melhoria na usabilidade dos formulários
- Interface mais intuitiva para os usuários
- Consistência visual em toda a aplicação
- Melhor indicação de quais campos são obrigatórios
