/**
 * @file App.tsx
 * @description Componente principal da aplicação Oluchy que configura o roteamento, autenticação,
 * e estrutura básica da interface do usuário. Gerencia a navegação entre páginas e controle de acesso
 * baseado em autenticação e permissões de usuário.
 *
 * @requires react
 * @requires react-router-dom
 * @requires @tanstack/react-query
 * @requires @/context/AuthContext
 * @requires @/context/SidebarContext
 * @requires @/integrations/supabase/client
 */

import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth } from "@/context/AuthContext";
import { SidebarProvider, useSidebar } from "@/context/SidebarContext";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "./components/Navbar";
import Sidebar from "./components/Sidebar";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import CashFlow from "./pages/CashFlow";
import Inventory from "./pages/Inventory";
import Sales from "./pages/Sales";
import Users from "./pages/Users";
import Settings from "./pages/Settings";
import Reports from "./pages/Reports";
import NotFound from "./pages/NotFound";

/**
 * Configuração do cliente de consulta do React Query
 * Define opções padrão para todas as consultas na aplicação
 */
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // Não recarrega dados quando a janela ganha foco
      staleTime: 1000 * 60 * 5, // Considera dados atuais por 5 minutos
    },
  },
});

/**
 * Componente de rota autenticada que verifica se o usuário está logado
 * antes de renderizar o conteúdo protegido
 *
 * @component
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Componentes filhos a serem renderizados se autenticado
 * @returns {JSX.Element} Componente renderizado com base no estado de autenticação
 */
const AuthenticatedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth(); // Obtém informações de autenticação do contexto
  const [sidebarOpen, setSidebarOpen] = useState(false); // Estado para controlar a visibilidade do sidebar em dispositivos móveis

  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin h-8 w-8 border-4 border-oluchys-accent rounded-full border-t-transparent"></div>
    </div>;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navbar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <Sidebar isOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <MainContent>
        {children}
      </MainContent>
    </div>
  );
};

/**
 * Componente de rota administrativa que verifica se o usuário tem permissões
 * de administrador antes de renderizar o conteúdo protegido
 *
 * @component
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Componentes filhos a serem renderizados se o usuário for admin
 * @returns {JSX.Element} Componente renderizado com base nas permissões do usuário
 */
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user } = useAuth(); // Obtém informações do usuário autenticado
  const location = useLocation(); // Hook para obter a localização atual para redirecionamento
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null); // Estado para armazenar se o usuário é admin
  const [loading, setLoading] = useState(true); // Estado para controlar o carregamento

  /**
   * Efeito para verificar se o usuário tem permissões de administrador
   * Consulta o perfil do usuário no Supabase e verifica se o papel é 'admin'
   */
  useEffect(() => {
    const checkAdmin = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error("Erro ao verificar permissões:", error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data.role === 'admin');
        }
      } catch (err) {
        console.error("Erro ao verificar permissões:", err);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user]); // Executa quando o usuário muda

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin h-8 w-8 border-4 border-oluchys-accent rounded-full border-t-transparent"></div>
    </div>;
  }

  if (isAdmin === false) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

/**
 * Componente que configura o roteamento da aplicação e o provedor de autenticação
 * Define todas as rotas disponíveis e seus respectivos componentes,
 * aplicando proteção de autenticação e verificação de permissões quando necessário
 *
 * @component
 * @returns {JSX.Element} Componente de roteamento com autenticação configurada
 */
const AppWithAuth = () => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Routes>
          <Route path="/login" element={<Login />} />

          <Route
            path="/"
            element={
              <AuthenticatedRoute>
                <Dashboard />
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/dashboard"
            element={
              <Navigate to="/" replace />
            }
          />

          <Route
            path="/cashflow"
            element={
              <AuthenticatedRoute>
                <CashFlow />
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/inventory"
            element={
              <AuthenticatedRoute>
                <Inventory />
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/sales"
            element={
              <AuthenticatedRoute>
                <Sales />
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/users"
            element={
              <AuthenticatedRoute>
                <AdminRoute>
                  <Users />
                </AdminRoute>
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/settings"
            element={
              <AuthenticatedRoute>
                <AdminRoute>
                  <Settings />
                </AdminRoute>
              </AuthenticatedRoute>
            }
          />

          <Route
            path="/reports"
            element={
              <AuthenticatedRoute>
                <AdminRoute>
                  <Reports />
                </AdminRoute>
              </AuthenticatedRoute>
            }
          />

          <Route
            path="*"
            element={
              <AuthenticatedRoute>
                <NotFound />
              </AuthenticatedRoute>
            }
          />
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  );
};

/**
 * Componente de conteúdo principal que se ajusta automaticamente
 * com base no estado do sidebar (expandido ou recolhido)
 *
 * @component
 * @param {Object} props - Propriedades do componente
 * @param {React.ReactNode} props.children - Conteúdo a ser renderizado dentro do componente
 * @returns {JSX.Element} Componente de conteúdo principal com margens ajustadas
 */
const MainContent = ({ children }: { children: React.ReactNode }) => {
  const { isCollapsed } = useSidebar(); // Obtém o estado do sidebar do contexto

  return (
    <main
      className={`pt-16 transition-all duration-300 ease-in-out ${isCollapsed ? 'md:ml-16' : 'md:ml-64'}`}
    >
      <div className="container mx-auto px-4 py-6 transition-all duration-300 ease-in-out">
        {children}
      </div>
    </main>
  );
};

/**
 * Componente principal da aplicação que configura os provedores globais
 * Inclui React Query para gerenciamento de estado e requisições,
 * TooltipProvider para tooltips consistentes, Toaster para notificações,
 * e SidebarProvider para gerenciar o estado da barra lateral
 *
 * @component
 * @returns {JSX.Element} Aplicação completa com todos os provedores configurados
 */
const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <SidebarProvider>
          <AppWithAuth />
        </SidebarProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
