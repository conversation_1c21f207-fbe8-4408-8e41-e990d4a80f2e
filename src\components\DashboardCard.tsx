
import React from "react";
import { cn } from "@/lib/utils";

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  icon,
  trend,
  className
}) => {
  // Format currency if the value is a string that contains currency format
  let displayValue = value;
  if (typeof value === "string" && value.includes("kz")) {
    displayValue = value;
  }

  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-xl p-6 bg-white dark:bg-gray-900 shadow-card hover:shadow-card-hover transition-all duration-300 border border-gray-100 dark:border-gray-800 animate-scale-in",
        className
      )}
    >
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
            {title}
          </p>
          <h3 className="font-semibold mb-4 text-base">{displayValue}</h3>

          {trend && (
            <div className="flex items-center text-xs font-medium">
              <span
                className={cn(
                  "flex items-center",
                  trend.isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                )}
              >
                {trend.isPositive ? "↑" : "↓"} {trend.value}%
              </span>
              <span className="ml-1 text-gray-500 dark:text-gray-400">
                {title === "Vendas de Hoje" ? "vs. dia anterior" : "vs. semana anterior"}
              </span>
            </div>
          )}
        </div>

        <div className="text-oluchys-accent">{icon}</div>
      </div>

      {/* Decorative element */}
      <div className="absolute -bottom-4 -right-4 w-24 h-24 rounded-full bg-gradient-to-br from-oluchys-accent/5 to-oluchys-accent/10"></div>
    </div>
  );
};

export default DashboardCard;
