
import React from "react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { cn } from "@/lib/utils";

interface ChartData {
  name: string;
  value: number;
}

interface DashboardChartProps {
  title: string;
  subtitle?: string;
  data: ChartData[];
  className?: string;
}

const DashboardChart: React.FC<DashboardChartProps> = ({
  title,
  subtitle,
  data,
  className,
}) => {
  // Determinar se o gráfico é de estoque (quantidade) ou vendas (valor)
  const isStockChart = title.toLowerCase().includes("estoque");

  // Função para formatar números com espaçamento adequado
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      minimumFractionDigits: isStockChart ? 0 : 2,
      maximumFractionDigits: isStockChart ? 0 : 2
    }).format(value);
  };

  // Determinar o nome da label com base no tipo de gráfico
  const valueLabel = isStockChart ? "Qt" : "Valor";
  return (
    <div
      className={cn(
        "rounded-xl p-6 bg-white dark:bg-gray-900 shadow-card border border-gray-100 dark:border-gray-800 animate-scale-in",
        className
      )}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold">{title}</h3>
        {subtitle && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {subtitle}
          </p>
        )}
      </div>

      <div className="h-[250px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{
              top: 5,
              right: 10,
              left: 30,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatNumber(value)}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                border: 'none'
              }}
              labelStyle={{ fontWeight: 600 }}
              formatter={(value) => [formatNumber(Number(value)), valueLabel]}
            />
            <Line
              type="monotone"
              dataKey="value"
              name={valueLabel}
              stroke="hsl(var(--oluchys-accent))"
              strokeWidth={2}
              dot={{ r: 4, strokeWidth: 2 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default DashboardChart;
