import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Upload, X } from "lucide-react";
import { Sale, PaymentMethod, ProductVariant, Profile, SaleStatus, DeliveryPerson } from "@/types";
import { Textarea } from "@/components/ui/textarea";
interface EditSaleFormProps {
  sale: Sale;
  onSubmit: (data: any, proofFile?: File) => void;
  onCancel: () => void;
  productVariants: ProductVariant[];
  paymentMethods: PaymentMethod[];
  statuses: SaleStatus[];
  sellers: Profile[];
  deliveryPersons: DeliveryPerson[];
  getPaymentProofUrl: (path?: string) => string | null;
}
const EditSaleForm: React.FC<EditSaleFormProps> = ({
  sale,
  onSubmit,
  onCancel,
  productVariants,
  paymentMethods,
  statuses,
  sellers,
  deliveryPersons,
  getPaymentProofUrl
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const formatDateForInput = (dateString: string) => {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };
  const form = useForm({
    defaultValues: {
      customer_name: sale.customer_name,
      customer_contact: sale.customer_contact || "",
      sale_date: sale.sale_date ? formatDateForInput(sale.sale_date) : "",
      product_variant_id: sale.product_variant_id,
      price: sale.price.toString(),
      payment_method_id: sale.payment_method_id,
      notes: sale.notes || "",
      delivery_person_id: sale.delivery_person_id || "",
      seller_id: sale.seller_id,
      status_id: sale.status_id
    }
  });
  useEffect(() => {
    if (sale.payment_proof) {
      const proofUrl = getPaymentProofUrl(sale.payment_proof);
      if (proofUrl) {
        setPreviewUrl(proofUrl);
      }
    }
  }, [sale.payment_proof, getPaymentProofUrl]);
  const handleProductChange = (productVariantId: string) => {
    const productVariant = productVariants.find(pv => pv.id === productVariantId);
    if (productVariant && productVariant.product) {
      form.setValue('price', productVariant.product.price.toString());
    }
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  const clearFileSelection = () => {
    setSelectedFile(null);
    if (!sale.payment_proof) {
      setPreviewUrl(null);
    } else {
      const proofUrl = getPaymentProofUrl(sale.payment_proof);
      if (proofUrl) {
        setPreviewUrl(proofUrl);
      }
    }
    const fileInput = document.getElementById('edit_payment_proof') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };
  const handleSubmit = (data: any) => {
    onSubmit(data, selectedFile || undefined);
  };
  return <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="modal-form">
        <div className="modal-form-grid">
          <FormField control={form.control} name="customer_name" render={({
          field
        }) => <FormItem>
                <FormLabel required>Cliente</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nome do cliente" required />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="customer_contact" render={({
          field
        }) => <FormItem>
                <FormLabel>Contacto</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <span className="mr-2 text-sm text-muted-foreground">+244</span>
                    <Input {...field} placeholder="Digite o número de telefone" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="sale_date" render={({
          field
        }) => <FormItem>
                <FormLabel required>Data da Venda</FormLabel>
                <FormControl>
                  <Input {...field} type="date" required />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="product_variant_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Produto</FormLabel>
                <Select onValueChange={value => {
            field.onChange(value);
            handleProductChange(value);
          }} value={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o produto" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {productVariants.map(pv => <SelectItem key={pv.id} value={pv.id}>
                        {pv.product?.name} - {pv.color?.name}, {pv.size?.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="price" render={({
          field
        }) => <FormItem>
                <FormLabel required>Preço</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <Input {...field} type="number" placeholder="0.00" step="0.01" required />
                    <span className="ml-2 text-sm text-muted-foreground">kz</span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="payment_method_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Forma de Pagamento</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {paymentMethods.map(method => <SelectItem key={method.id} value={method.id}>
                        {method.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <div className="col-span-1 md:col-span-2">
            <FormItem>
              <FormLabel>Comprovativo</FormLabel>
              <div className="mt-1">
                <Input id="edit_payment_proof" type="file" accept="image/*" className="hidden" onChange={handleFileChange} />
                {!previewUrl ? <Button type="button" variant="outline" onClick={() => document.getElementById("edit_payment_proof")?.click()} className="w-full flex items-center justify-center h-24 border-dashed">
                    <Upload className="h-4 w-4 mr-2" />
                    Selecionar comprovativo
                  </Button> : <div className="relative rounded-md overflow-hidden border border-gray-200 dark:border-gray-700">
                    <img src={previewUrl} alt="Preview" className="w-full h-32 object-cover" onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder.svg';
                  target.alt = 'Erro ao carregar imagem';
                }} />
                    <Button type="button" variant="destructive" size="icon" className="absolute top-2 right-2 h-6 w-6" onClick={clearFileSelection}>
                      <X className="h-4 w-4" />
                    </Button>
                    {selectedFile && <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                        Nova imagem selecionada
                      </div>}
                  </div>}
              </div>
            </FormItem>
          </div>

          <FormField control={form.control} name="delivery_person_id" render={({
          field
        }) => <FormItem>
                <FormLabel>Entregador</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ""}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o entregador (opcional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {deliveryPersons.map(person => <SelectItem key={person.id} value={person.id}>
                        {person.name} {person.phone && `- ${person.phone}`}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="seller_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Vendedor</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o vendedor" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {sellers.map(seller => <SelectItem key={seller.id} value={seller.id}>
                        {seller.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="status_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {statuses.map(status => <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />
        </div>

        <FormField control={form.control} name="notes" render={({
        field
      }) => <FormItem>
              <FormLabel>Observações</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Observações (opcional)" />
              </FormControl>
              <FormMessage />
            </FormItem>} />

        <div className="text-xs text-gray-500 mb-4">
          <span className="text-destructive">*</span> Campos obrigatórios
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit" className="my-0">Salvar</Button>
        </DialogFooter>
      </form>
    </Form>;
};
export default EditSaleForm;