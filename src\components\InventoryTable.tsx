/**
 * @file InventoryTable.tsx
 * @description Componente de tabela de estoque que exibe, filtra e permite gerenciar
 * os produtos e suas variações. Inclui funcionalidades de busca, filtragem por categoria,
 * cor e tamanho, paginação, edição e exclusão de produtos.
 */

import React, { useState, useEffect } from "react";
import { Package, Search, Edit, Trash2, Filter, Loader2, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn, formatCurrency } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import NewProductForm from "@/components/NewProductForm";
import { ProductVariant } from "@/types";
import { updateProductVariant, deleteProductVariant, updateProduct } from "@/services/productService";
import { Category } from "@/services/categoryService";
import { Color } from "@/services/colorService";
import { Size } from "@/services/sizeService";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import DataTablePagination from "@/components/ui/data-table-pagination";
import { paginateData } from "@/lib/pagination";

/**
 * Props para o componente InventoryTable
 *
 * @interface InventoryTableProps
 */
interface InventoryTableProps {
  /** Lista de variações de produtos a serem exibidas na tabela */
  productVariants: ProductVariant[];
  /** Indica se os dados estão sendo carregados */
  isLoading: boolean;
  /** Função para recarregar a lista de produtos */
  refetchProducts: () => void;
  /** Função chamada quando o botão de adicionar produto é clicado */
  onAddProductClick: () => void;
  /** Lista de categorias disponíveis para filtragem */
  categories: Category[];
  /** Lista de cores disponíveis para filtragem */
  colors: Color[];
  /** Lista de tamanhos disponíveis para filtragem */
  sizes: Size[];
  /** Controla se o usuário pode adicionar novos produtos */
  canAddProducts?: boolean;
  /** Controla se o usuário pode editar produtos existentes */
  canEditProducts?: boolean;
  /** Controla se o usuário pode excluir produtos */
  canDeleteProducts?: boolean;
}

/**
 * Componente de tabela de estoque com funcionalidades de filtragem, paginação e gestão
 *
 * @component
 * @param {InventoryTableProps} props - Props do componente
 * @returns {JSX.Element} Componente InventoryTable renderizado
 */
const InventoryTable: React.FC<InventoryTableProps> = ({
  productVariants,
  isLoading,
  refetchProducts,
  onAddProductClick,
  categories,
  colors,
  sizes,
  canAddProducts = true,
  canEditProducts = true,
  canDeleteProducts = true,
}) => {
  // Estados para controle de filtros e ações
  const [searchTerm, setSearchTerm] = useState(""); // Termo de busca
  const [filterCategory, setFilterCategory] = useState<string | null>(null); // Filtro por categoria
  const [filterColor, setFilterColor] = useState<string | null>(null); // Filtro por cor
  const [filterSize, setFilterSize] = useState<string | null>(null); // Filtro por tamanho
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null); // Variação selecionada para edição/exclusão
  const [showDeleteDialog, setShowDeleteDialog] = useState(false); // Controla diálogo de confirmação de exclusão
  const [showEditDialog, setShowEditDialog] = useState(false); // Controla diálogo de edição

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1); // Página atual
  const [pageSize, setPageSize] = useState(10); // Tamanho da página

  const uniqueCategories = [...new Set(productVariants.map(variant => variant.product?.category?.name))].filter(Boolean) as string[];
  const uniqueColors = [...new Set(productVariants.map(variant => variant.color?.name))].filter(Boolean) as string[];
  const uniqueSizes = [...new Set(productVariants.map(variant => variant.size?.name))].filter(Boolean) as string[];

  const filteredProducts = productVariants.filter(variant => {
    const productName = variant.product?.name || '';
    const colorName = variant.color?.name || '';
    const sizeName = variant.size?.name || '';
    const categoryName = variant.product?.category?.name || '';

    return (
      (searchTerm === "" ||
       productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
       colorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
       sizeName.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (filterCategory === null || categoryName === filterCategory) &&
      (filterColor === null || colorName === filterColor) &&
      (filterSize === null || sizeName === filterSize)
    );
  });

  const handleEdit = (variant: ProductVariant) => {
    if (!variant.product) return;

    console.log("Editing variant:", variant);
    setSelectedVariant(variant);
    setShowEditDialog(true);
  };

  const handleDelete = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!selectedVariant) return;

    try {
      await deleteProductVariant(selectedVariant.id);
      refetchProducts();
      setShowDeleteDialog(false);
      setSelectedVariant(null);
      toast.success("Produto removido com sucesso");
    } catch (error) {
      console.error("Erro ao excluir produto:", error);
      toast.error("Erro ao excluir produto");
    }
  };

  const handleEditSubmit = async (data: any) => {
    if (!selectedVariant || !selectedVariant.product) return;

    try {
      // Validações
      if (!data.category_id) {
        throw new Error("Categoria é obrigatória");
      }
      if (!data.color_id) {
        throw new Error("Cor é obrigatória");
      }
      if (!data.size_id) {
        throw new Error("Tamanho é obrigatório");
      }

      console.log("Produto ID para atualização:", selectedVariant.product.id);

      // Primeiro atualize o produto
      await updateProduct(selectedVariant.product.id, {
        name: data.name,
        price: parseFloat(data.price),
        category_id: data.category_id,
      });

      // Depois atualize a variante
      await updateProductVariant(selectedVariant.id, {
        quantity: parseInt(data.quantity),
        color_id: data.color_id,
        size_id: data.size_id,
      });

      refetchProducts();
      setShowEditDialog(false);
      setSelectedVariant(null);

      // Único toast.success aqui - removida duplicação
      toast.success("Produto atualizado com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar produto:", error);
      toast.error(`Erro ao atualizar produto: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  const resetFilters = () => {
    setFilterCategory(null);
    setFilterColor(null);
    setFilterSize(null);
  };

  const getColorHexCode = (colorName: string) => {
    const color = colors.find(c => c.name === colorName);
    return color?.hexCode || "#CCCCCC";
  };

  // Resetar para a primeira página quando os filtros mudarem
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterCategory, filterColor, filterSize]);

  // Aplicar paginação aos produtos filtrados
  const { paginatedData, totalItems, totalPages } = paginateData(
    filteredProducts,
    currentPage,
    pageSize
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho da página
  };

  /**
   * Renderiza o conteúdo da tabela com base no estado de carregamento e nos dados filtrados
   * Exibe mensagens apropriadas quando está carregando ou quando não há resultados
   *
   * @returns {JSX.Element} Conteúdo da tabela a ser renderizado
   */
  const renderTableContent = () => {
    if (isLoading) {
      return (
        <TableRow>
          <TableCell colSpan={8} className="h-48 text-center">
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
              <p className="text-gray-500">Carregando produtos...</p>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    if (filteredProducts.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={8} className="h-48">
            <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
              <Package className="h-12 w-12 mb-4 opacity-20" />
              <h3 className="text-lg font-medium mb-1">Nenhum produto encontrado</h3>
              <p className="text-sm">Tente ajustar os filtros de busca</p>
            </div>
          </TableCell>
        </TableRow>
      );
    }

    return paginatedData.map((variant) => (
      <TableRow
        key={variant.id}
        className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
      >
        <TableCell className="font-medium">{variant.product?.name || 'Produto Desconhecido'}</TableCell>
        <TableCell className="text-gray-500 dark:text-gray-400">
          {variant.product?.category?.name ||
           (variant.product?.category_id ?
             categories.find(cat => cat.id === variant.product?.category_id)?.name || 'Sem Categoria' :
             'Sem Categoria')
          }
        </TableCell>
        <TableCell>
          <div className="flex items-center">
            <div
              className="w-4 h-4 rounded-full mr-2"
              style={{ backgroundColor: getColorHexCode(variant.color?.name || '') }}
            ></div>
            {variant.color?.name || 'Sem Cor'}
          </div>
        </TableCell>
        <TableCell>{variant.size?.name || 'Sem Tamanho'}</TableCell>
        <TableCell className="text-right">
          <span
            className={cn(
              "px-2 py-1 rounded-full text-xs font-medium",
              variant.quantity > 10
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : variant.quantity > 5
                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
            )}
          >
            {variant.quantity}
          </span>
        </TableCell>
        <TableCell className="text-right font-medium">
          {formatCurrency(variant.product?.price || 0)}
        </TableCell>
        <TableCell className="text-right font-medium">
          {formatCurrency((variant.product?.price || 0) * variant.quantity)}
        </TableCell>
        <TableCell className="text-right">
          <div className="flex justify-end gap-2">
            {canEditProducts ? (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handleEdit(variant)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            ) : (
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-60"
                        disabled
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>Você não tem permissão para editar produtos</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {canDeleteProducts ? (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                onClick={() => handleDelete(variant)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            ) : (
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-500 opacity-60"
                        disabled
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>Você não tem permissão para excluir produtos</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <div className={cn("bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 animate-scale-in")}>
      <div className="p-6 border-b border-gray-100 dark:border-gray-800">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-0">
          <h2 className="text-lg font-semibold flex items-center">
            <Package className="h-5 w-5 mr-2 text-primary" />
            Estoque de Produtos
          </h2>
        </div>

        {/* Layout para dispositivos móveis */}
        <div className="flex flex-col gap-3 sm:hidden">
          {/* Campo de busca */}
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Buscar produto..."
              className="pl-9 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filtros */}
          <div className="flex flex-col gap-2 w-full">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-10 w-full justify-between">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    Categoria {filterCategory ? `(${filterCategory})` : ''}
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-2">
                <div className="space-y-2">
                  <Button
                    variant={filterCategory === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterCategory(null)}
                    className="text-xs w-full justify-start"
                  >
                    Todos
                  </Button>
                  {uniqueCategories.map((category) => (
                    <Button
                      key={category}
                      variant={filterCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterCategory(category)}
                      className="text-xs w-full justify-start"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-10 w-full justify-between">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    Cor {filterColor ? `(${filterColor})` : ''}
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-2">
                <div className="space-y-2">
                  <Button
                    variant={filterColor === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterColor(null)}
                    className="text-xs w-full justify-start"
                  >
                    Todas
                  </Button>
                  {uniqueColors.map((color) => (
                    <Button
                      key={color}
                      variant={filterColor === color ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterColor(color)}
                      className="text-xs w-full justify-start"
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: getColorHexCode(color) }}
                      ></div>
                      {color}
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-10 w-full justify-between">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    Tamanho {filterSize ? `(${filterSize})` : ''}
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-2">
                <div className="space-y-2">
                  <Button
                    variant={filterSize === null ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterSize(null)}
                    className="text-xs w-full justify-start"
                  >
                    Todos
                  </Button>
                  {uniqueSizes.map((size) => (
                    <Button
                      key={size}
                      variant={filterSize === size ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterSize(size)}
                      className="text-xs w-full justify-start"
                    >
                      {size}
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            {(filterCategory || filterColor || filterSize) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-xs w-full h-10"
              >
                Limpar filtros
              </Button>
            )}
          </div>

          {/* Botão Adicionar Produto */}
          {canAddProducts ? (
            <Button onClick={onAddProductClick} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Produto
            </Button>
          ) : (
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="w-full">
                    <Button disabled className="w-full opacity-60">
                      <Plus className="h-4 w-4 mr-2" />
                      Adicionar Produto
                    </Button>
                  </span>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Você não tem permissão para adicionar produtos</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2 mt-3">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>
        </div>

        {/* Layout para desktop */}
        <div className="hidden sm:flex sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>

          {/* Controles de busca, filtros e botão */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Buscar produto..."
                className="pl-9 w-[180px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="text-xs gap-1 h-10">
                    <Filter className="h-3 w-3" />
                    Categoria {filterCategory ? `(${filterCategory})` : ''}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <div className="space-y-2">
                    <Button
                      variant={filterCategory === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterCategory(null)}
                      className="text-xs w-full justify-start"
                    >
                      Todos
                    </Button>
                    {uniqueCategories.map((category) => (
                      <Button
                        key={category}
                        variant={filterCategory === category ? "default" : "outline"}
                        size="sm"
                        onClick={() => setFilterCategory(category)}
                        className="text-xs w-full justify-start"
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="text-xs gap-1 h-10">
                    <Filter className="h-3 w-3" />
                    Cor {filterColor ? `(${filterColor})` : ''}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <div className="space-y-2">
                    <Button
                      variant={filterColor === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterColor(null)}
                      className="text-xs w-full justify-start"
                    >
                      Todas
                    </Button>
                    {uniqueColors.map((color) => (
                      <Button
                        key={color}
                        variant={filterColor === color ? "default" : "outline"}
                        size="sm"
                        onClick={() => setFilterColor(color)}
                        className="text-xs w-full justify-start"
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: getColorHexCode(color) }}
                        ></div>
                        {color}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="text-xs gap-1 h-10">
                    <Filter className="h-3 w-3" />
                    Tamanho {filterSize ? `(${filterSize})` : ''}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <div className="space-y-2">
                    <Button
                      variant={filterSize === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterSize(null)}
                      className="text-xs w-full justify-start"
                    >
                      Todos
                    </Button>
                    {uniqueSizes.map((size) => (
                      <Button
                        key={size}
                        variant={filterSize === size ? "default" : "outline"}
                        size="sm"
                        onClick={() => setFilterSize(size)}
                        className="text-xs w-full justify-start"
                      >
                        {size}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {(filterCategory || filterColor || filterSize) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="text-xs h-10"
                >
                  Limpar filtros
                </Button>
              )}
            </div>

            {canAddProducts ? (
              <Button onClick={onAddProductClick} className="gap-1 h-10">
                <Plus className="h-4 w-4" />
                Adicionar
              </Button>
            ) : (
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span>
                      <Button disabled className="gap-1 h-10 opacity-60">
                        <Plus className="h-4 w-4" />
                        Adicionar
                      </Button>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>Você não tem permissão para adicionar produtos</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Produto</TableHead>
              <TableHead>Categoria</TableHead>
              <TableHead>Cor</TableHead>
              <TableHead>Tamanho</TableHead>
              <TableHead className="text-center">Qt</TableHead>
              <TableHead className="text-center">P/U</TableHead>
              <TableHead className="text-center">P/Total</TableHead>
              <TableHead className="text-center">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {renderTableContent()}
          </TableBody>
        </Table>
      </div>

      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Produto</DialogTitle>
            <DialogDescription>
              Atualize os detalhes do produto selecionado.
            </DialogDescription>
          </DialogHeader>
          {selectedVariant && selectedVariant.product && (
            <NewProductForm
              onSubmit={handleEditSubmit}
              onCancel={() => setShowEditDialog(false)}
              categories={categories}
              colors={colors}
              sizes={sizes}
              initialData={{
                name: selectedVariant.product.name,
                category_id: selectedVariant.product.category_id,
                color_id: selectedVariant.color_id,
                size_id: selectedVariant.size_id,
                quantity: selectedVariant.quantity.toString(),
                price: selectedVariant.product.price.toString()
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Componente de paginação */}
      {!isLoading && filteredProducts.length > 0 && (
        <DataTablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirmar exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o produto "{selectedVariant?.product?.name}"
              ({selectedVariant?.color?.name}, {selectedVariant?.size?.name})?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Excluir
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryTable;
