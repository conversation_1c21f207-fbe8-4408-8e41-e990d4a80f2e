/**
 * @file Navbar.tsx
 * @description Componente de barra de navegação superior que exibe o logo, controles do sidebar,
 * notificações, alternador de tema, perfil do usuário e botão de logout.
 * Responsável por fornecer acesso rápido a funções importantes e exibir informações do usuário logado.
 */

import React, { useState, useEffect } from "react";
import { Bell, BellRing, ShoppingCart, AlertTriangle, Menu, X, LayoutDashboard, LogOut, ChevronLeft, ChevronRight } from "lucide-react";
import ThemeToggle from "./ThemeToggle";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useToast } from "@/hooks/use-toast";
import { useSidebar } from "@/context/SidebarContext";
import { useAuth } from "@/context/AuthContext";
import NotificationBadge from "./NotificationBadge";
import { useNotifications } from "@/hooks/use-notifications";
import ProfileEditModal from "./ProfileEditModal";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

/**
 * Props para o componente Navbar
 *
 * @interface NavbarProps
 */
interface NavbarProps {
  /** Estado atual do sidebar em dispositivos móveis (aberto/fechado) */
  sidebarOpen: boolean;
  /** Função para alterar o estado do sidebar em dispositivos móveis */
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

/**
 * Componente de barra de navegação superior
 *
 * @component
 * @param {NavbarProps} props - Props do componente
 * @returns {JSX.Element} Componente Navbar renderizado
 *
 * @example
 * // Em App.tsx ou outro componente de layout
 * <Navbar
 *   sidebarOpen={sidebarOpen}
 *   setSidebarOpen={setSidebarOpen}
 * />
 */
const Navbar: React.FC<NavbarProps> = ({
  sidebarOpen,
  setSidebarOpen
}) => {
  // Hooks para funcionalidades e estado
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const { user, profile, signOut } = useAuth();
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const { isCollapsed, toggleCollapsed } = useSidebar();
  const [showProfileModal, setShowProfileModal] = useState(false);

  /**
   * Alterna o estado de abertura do sidebar em dispositivos móveis
   */
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  /**
   * Realiza o logout do usuário atual
   */
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  /**
   * Retorna o ícone apropriado com base no tipo de notificação
   *
   * @param {string} type - Tipo da notificação ('venda', 'estoque', etc.)
   * @returns {JSX.Element} Ícone correspondente ao tipo de notificação
   */
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'venda': return <ShoppingCart className="h-4 w-4 text-green-600" />;
      case 'estoque': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default: return <BellRing className="h-4 w-4" />;
    }
  };

  /**
   * Gera as iniciais do nome do usuário para exibição no avatar
   *
   * @param {string} name - Nome completo do usuário
   * @returns {string} Iniciais do nome (até 2 caracteres)
   */
  const getInitials = (name: string) => {
    return name.split(" ").map(n => n[0]).join("").toUpperCase().substring(0, 2);
  };

  return <nav className="fixed top-0 right-0 left-0 z-30 bg-white/80 dark:bg-black/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800 px-4 h-16 flex items-center justify-between transition-all duration-300 ease-in-out">
      <div className="flex items-center">
        {isMobile && <Button variant="ghost" size="icon" className="mr-2" onClick={toggleSidebar} aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}>
            {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>}

        <div className="flex items-center">
          <LayoutDashboard className="h-5 w-5 text-oluchys-accent mr-2" />
          <h1 className="font-semibold text-lg">Oluchys</h1>
        </div>

        {!isMobile && (
          <Button
            variant="ghost"
            size="icon"
            className="ml-4"
            onClick={toggleCollapsed}
            aria-label={isCollapsed ? "Expandir sidebar" : "Colapsar sidebar"}
          >
            {isCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        )}
      </div>

      <div className="flex items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative ml-2" aria-label="Notifications">
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && <NotificationBadge count={unreadCount} />}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80 mx-[40px]">
            <DropdownMenuLabel className="flex items-center justify-between">
              <span>Notificações</span>
              {unreadCount > 0 && <Button variant="ghost" size="sm" className="text-xs h-7" onClick={() => markAllAsRead()}>
                  Marcar todas como lidas
                </Button>}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-[300px] overflow-y-auto">
              {notifications.length > 0 ? notifications.map(notification => (
                <DropdownMenuItem
                  key={notification.id}
                  className={`flex flex-col items-start py-2 ${!notification.read ? 'bg-gray-50 dark:bg-gray-800' : ''}`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex w-full justify-between items-center">
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(notification.type)}
                      <span className="font-medium text-sm">{notification.message}</span>
                    </div>
                    {!notification.read && <span className="h-2 w-2 rounded-full bg-blue-500"></span>}
                  </div>
                  <span className="text-xs text-gray-500 mt-1">
                    {new Date(notification.created_at).toLocaleString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </DropdownMenuItem>
              )) : <div className="py-4 text-center text-sm text-gray-500">
                  Nenhuma notificação
                </div>}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        <ThemeToggle />

        <div className="ml-4 flex items-center">
          <button className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full px-2 py-1 transition-colors" onClick={() => setShowProfileModal(true)}>
            <Avatar className="w-8 h-8">
              {profile?.avatar_url ? <AvatarImage src={profile.avatar_url} alt="Foto de perfil" /> : <AvatarFallback className="text-xs font-medium">
                  {profile?.name ? getInitials(profile.name) : user?.email?.substring(0, 2).toUpperCase() || "OU"}
                </AvatarFallback>}
            </Avatar>
            <span className="font-medium text-sm hidden sm:inline-block">
              {profile?.name || user?.email?.split("@")[0] || "Admin"}
            </span>
          </button>
        </div>

        <Button variant="ghost" size="icon" className="ml-2" onClick={handleSignOut} aria-label="Sign out">
          <LogOut className="h-5 w-5" />
        </Button>
      </div>

      <ProfileEditModal open={showProfileModal} onOpenChange={setShowProfileModal} />
    </nav>;
};

export default Navbar;
