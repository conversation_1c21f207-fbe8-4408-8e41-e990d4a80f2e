import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { z } from "zod";
import { Category } from "@/services/categoryService";
import { Color } from "@/services/colorService";
import { Size } from "@/services/sizeService";
interface NewProductFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  initialData?: {
    name: string;
    category_id: string;
    color_id: string;
    size_id: string;
    quantity: string;
    price: string;
  };
  categories: Category[];
  colors: Color[];
  sizes: Size[];
}
const formSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  category_id: z.string().min(1, "Categoria é obrigatória"),
  color_id: z.string().min(1, "Cor é obrigatória"),
  size_id: z.string().min(1, "Tamanho é obrigatório"),
  quantity: z.string().min(1, "Quantidade é obrigatória").refine(val => !isNaN(Number(val)), "Deve ser um número válido").refine(val => Number(val) >= 0, "Deve ser maior ou igual a zero"),
  price: z.string().min(1, "Preço é obrigatório").refine(val => !isNaN(Number(val)), "Deve ser um número válido").refine(val => Number(val) > 0, "Deve ser maior que zero")
});
const NewProductForm: React.FC<NewProductFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  categories,
  colors,
  sizes
}) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      category_id: "",
      color_id: "",
      size_id: "",
      quantity: "",
      price: ""
    }
  });
  const handleSubmit = (data: z.infer<typeof formSchema>) => {
    onSubmit(data);
  };
  return <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField control={form.control} name="name" render={({
          field
        }) => <FormItem>
                <FormLabel required>Nome do Produto</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nome do produto" />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="category_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Categoria</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a categoria" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map(category => <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="color_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Cor</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a cor" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {colors.map(color => <SelectItem key={color.id} value={color.id}>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full mr-2" style={{
                    backgroundColor: color.hexCode
                  }}></div>
                          {color.name}
                        </div>
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="size_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Tamanho</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tamanho" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {sizes.map(size => <SelectItem key={size.id} value={size.id}>
                        {size.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="quantity" render={({
          field
        }) => <FormItem>
                <FormLabel required>Quantidade</FormLabel>
                <FormControl>
                  <Input {...field} type="number" placeholder="0" min="0" />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="price" render={({
          field
        }) => <FormItem>
                <FormLabel required>P/U (kz)</FormLabel>
                <FormControl>
                  <Input {...field} type="number" placeholder="0.00" step="0.01" min="0" />
                </FormControl>
                <FormMessage />
              </FormItem>} />
        </div>

        <div className="text-xs text-gray-500 mb-4">
          <span className="text-destructive">*</span> Campos obrigatórios
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit" className="my-0">Salvar</Button>
        </DialogFooter>
      </form>
    </Form>;
};
export default NewProductForm;