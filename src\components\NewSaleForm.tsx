import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Upload, X } from "lucide-react";
import { PaymentMethod, ProductVariant, Profile, SaleStatus, DeliveryPerson } from "@/types";
import { Textarea } from "@/components/ui/textarea";
interface NewSaleFormProps {
  onSubmit: (data: any, proofFile?: File) => void;
  onCancel: () => void;
  productVariants: ProductVariant[];
  paymentMethods: PaymentMethod[];
  statuses: SaleStatus[];
  sellers: Profile[];
  deliveryPersons: DeliveryPerson[];
}
const NewSaleForm: React.FC<NewSaleFormProps> = ({
  onSubmit,
  onCancel,
  productVariants,
  paymentMethods,
  statuses,
  sellers,
  deliveryPersons
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const form = useForm({
    defaultValues: {
      customer_name: "",
      customer_contact: "",
      sale_date: new Date().toISOString().split('T')[0],
      product_variant_id: "",
      price: "",
      payment_method_id: "",
      notes: "",
      delivery_person_id: "",
      seller_id: "",
      status_id: ""
    }
  });
  const handleProductChange = (productVariantId: string) => {
    const productVariant = productVariants.find(pv => pv.id === productVariantId);
    if (productVariant && productVariant.product) {
      form.setValue('price', productVariant.product.price.toString());
    }
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const fileType = file.type;
      if (!fileType.match(/^image\/(jpeg|jpg|png)$/) && fileType !== 'application/pdf') {
        alert('Apenas arquivos PNG, JPG ou PDF são permitidos');
        return;
      }
      setSelectedFile(file);
      if (fileType.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setPreviewUrl('pdf');
      }
    }
  };
  const clearFileSelection = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    const fileInput = document.getElementById('payment_proof') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };
  const handleSubmit = (data: any) => {
    if (!data.customer_name || !data.product_variant_id || !data.price || !data.payment_method_id || !data.seller_id || !data.status_id) {
      alert('Por favor, preencha todos os campos obrigatórios');
      return;
    }
    onSubmit(data, selectedFile || undefined);
  };
  return <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="modal-form">
        <div className="modal-form-grid">
          <FormField control={form.control} name="customer_name" render={({
          field
        }) => <FormItem>
                <FormLabel required>Cliente</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nome do cliente" required />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="customer_contact" render={({
          field
        }) => <FormItem>
                <FormLabel>Contacto</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <span className="mr-2 text-sm text-muted-foreground">+244</span>
                    <Input {...field} placeholder="Digite o número de telefone" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="sale_date" render={({
          field
        }) => <FormItem>
                <FormLabel required>Data da Venda</FormLabel>
                <FormControl>
                  <Input {...field} type="date" required />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="product_variant_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Produto</FormLabel>
                <Select onValueChange={value => {
            field.onChange(value);
            handleProductChange(value);
          }} defaultValue={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o produto" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {productVariants.map(pv => <SelectItem key={pv.id} value={pv.id}>
                        {pv.product?.name} - {pv.color?.name}, {pv.size?.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="price" render={({
          field
        }) => <FormItem>
                <FormLabel required>Preço</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <Input {...field} type="number" placeholder="0.00" step="0.01" required />
                    <span className="ml-2 text-sm text-muted-foreground">kz</span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="payment_method_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Forma de Pagamento</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {paymentMethods.map(method => <SelectItem key={method.id} value={method.id}>
                        {method.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <div className="col-span-1 md:col-span-2">
            <FormItem>
              <FormLabel>Comprovativo</FormLabel>
              <div className="mt-1">
                <Input id="payment_proof" type="file" accept="image/png,image/jpeg,image/jpg,application/pdf" className="hidden" onChange={handleFileChange} />
                {!previewUrl ? <Button type="button" variant="outline" onClick={() => document.getElementById("payment_proof")?.click()} className="w-full flex items-center justify-center h-24 border-dashed">
                    <Upload className="h-4 w-4 mr-2" />
                    Selecionar comprovativo (PNG, JPG, PDF)
                  </Button> : <div className="relative rounded-md overflow-hidden border border-gray-200 dark:border-gray-700">
                    {previewUrl !== 'pdf' ? <img src={previewUrl} alt="Preview" className="w-full h-32 object-cover" /> : <div className="w-full h-32 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                        <p className="text-sm text-gray-500">
                          {selectedFile?.name} (PDF)
                        </p>
                      </div>}
                    <Button type="button" variant="destructive" size="icon" className="absolute top-2 right-2 h-6 w-6" onClick={clearFileSelection}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>}
              </div>
            </FormItem>
          </div>

          <FormField control={form.control} name="delivery_person_id" render={({
          field
        }) => <FormItem>
                <FormLabel>Entregador</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o entregador (opcional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {deliveryPersons.map(person => <SelectItem key={person.id} value={person.id}>
                        {person.name} {person.phone && `- ${person.phone}`}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="seller_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Vendedor</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o vendedor" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {sellers.map(seller => <SelectItem key={seller.id} value={seller.id}>
                        {seller.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="status_id" render={({
          field
        }) => <FormItem>
                <FormLabel required>Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} required>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {statuses.map(status => <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />
        </div>

        <FormField control={form.control} name="notes" render={({
        field
      }) => <FormItem>
              <FormLabel>Observações</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Observações (opcional)" />
              </FormControl>
              <FormMessage />
            </FormItem>} />

        <div className="text-xs text-gray-500 mb-4">
          <span className="text-destructive">*</span> Campos obrigatórios
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit" className="my-0">Salvar</Button>
        </DialogFooter>
      </form>
    </Form>;
};
export default NewSaleForm;