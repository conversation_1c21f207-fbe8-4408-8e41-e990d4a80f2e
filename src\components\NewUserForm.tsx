import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Eye, EyeOff } from "lucide-react";
import { UserRole } from "../pages/Users";
interface NewUserFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
}
const roleOptions = [{
  value: "admin",
  label: "Administrador"
}, {
  value: "vendedor",
  label: "Vendedor"
}, {
  value: "motoqueiro",
  label: "Motoqueiro"
}];

// Schema de validação
const userSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres"),
  phone: z.string().optional(),
  role: z.enum(["admin", "vendedor", "motoqueiro"]),
  active: z.boolean().default(true)
});
type UserFormValues = z.infer<typeof userSchema>;
const NewUserForm: React.FC<NewUserFormProps> = ({
  onSubmit,
  onCancel
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      phone: "",
      role: "vendedor" as UserRole,
      active: true
    }
  });
  const handleSubmit = async (data: UserFormValues) => {
    setLoading(true);
    try {
      await onSubmit(data);
    } finally {
      setLoading(false);
    }
  };
  return <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField control={form.control} name="name" render={({
          field
        }) => <FormItem>
                <FormLabel required>Nome</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nome completo" />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="email" render={({
          field
        }) => <FormItem>
                <FormLabel required>Email</FormLabel>
                <FormControl>
                  <Input {...field} type="email" placeholder="<EMAIL>" />
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="password" render={({
          field
        }) => <FormItem>
                <FormLabel required>Senha</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input {...field} type={showPassword ? "text" : "password"} placeholder="Senha para acesso ao sistema" />
                    <Button type="button" variant="ghost" size="sm" className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent" onClick={() => setShowPassword(!showPassword)}>
                      {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                    </Button>
                  </div>
                </FormControl>
                <FormDescription className="text-xs">
                  Mínimo de 6 caracteres
                </FormDescription>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="phone" render={({
          field
        }) => <FormItem>
                <FormLabel>Telefone</FormLabel>
                <FormControl>
                  <div className="flex items-center">
                    <span className="mr-2 text-sm text-muted-foreground">+244</span>
                    <Input {...field} placeholder="Digite o número de telefone" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="role" render={({
          field
        }) => <FormItem>
                <FormLabel required>Função</FormLabel>
                <Select onValueChange={field.onChange as (value: string) => void} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a função" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {roleOptions.map(role => <SelectItem key={role.value} value={role.value}>
                        {role.label}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>} />

          <FormField control={form.control} name="active" render={({
          field
        }) => <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Status do Usuário</FormLabel>
                  <FormDescription>
                    {field.value ? "Usuário ativo no sistema" : "Usuário será criado inativo"}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
              </FormItem>} />
        </div>

        <div className="text-xs text-gray-500 mb-4">
          <span className="text-destructive">*</span> Campos obrigatórios
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
            Cancelar
          </Button>
          <Button type="submit" disabled={loading} className="gap-2 my-[15px]">
            {loading && <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>}
            Adicionar Usuário
          </Button>
        </DialogFooter>
      </form>
    </Form>;
};
export default NewUserForm;