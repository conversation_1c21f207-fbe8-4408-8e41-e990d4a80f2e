import React from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

/**
 * Componente de teste para demonstrar os diferentes tipos de notificações
 *
 * @component
 * @returns {JSX.Element} Componente com botões para testar as notificações
 */
const NotificationTest: React.FC = () => {
  const showSuccessToast = () => {
    toast.success("Hi", {
      description: "Success message"
    });
  };

  const showErrorToast = () => {
    toast.error("Hi", {
      description: "Error message"
    });
  };

  const showInfoToast = () => {
    toast.info("Hi", {
      description: "Information message"
    });
  };

  const showWarningToast = () => {
    toast.warning("Hi", {
      description: "Warning message"
    });
  };

  const showDefaultToast = () => {
    toast("Hi", {
      description: "Custom message"
    });
  };

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Teste de Notificações</h2>
      <div className="flex flex-wrap gap-3">
        <Button onClick={showSuccessToast} className="bg-[#10b981] hover:bg-[#10b981]/90">
          Sucesso
        </Button>
        <Button onClick={showErrorToast} className="bg-[#ef4444] hover:bg-[#ef4444]/90">
          Erro
        </Button>
        <Button onClick={showInfoToast} className="bg-[#3b82f6] hover:bg-[#3b82f6]/90">
          Informação
        </Button>
        <Button onClick={showWarningToast} className="bg-[#f59e0b] hover:bg-[#f59e0b]/90">
          Aviso
        </Button>
        <Button onClick={showDefaultToast}>
          Padrão
        </Button>
      </div>
    </div>
  );
};

export default NotificationTest;
