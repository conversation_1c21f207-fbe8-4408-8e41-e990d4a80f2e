import React, { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Image, Trash2 } from "lucide-react";
import { Profile } from "@/types";

// Definição do schema de validação
const profileFormSchema = z.object({
  name: z.string().min(3, {
    message: "O nome deve ter pelo menos 3 caracteres."
  }),
  password: z.string().min(6, {
    message: "A senha deve ter pelo menos 6 caracteres."
  }).optional().or(z.literal(""))
});
type ProfileFormValues = z.infer<typeof profileFormSchema>;
interface ProfileEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
const ProfileEditModal: React.FC<ProfileEditModalProps> = ({
  open,
  onOpenChange
}) => {
  const {
    user,
    profile
  } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(profile?.avatar_url || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Inicializar o formulário com os dados do perfil
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: profile?.name || "",
      password: ""
    }
  });

  // Função para abrir o seletor de arquivo
  const handleAvatarChange = () => {
    fileInputRef.current?.click();
  };

  // Função para processar o upload da imagem
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0] || !user) return;
    const file = e.target.files[0];
    setIsLoading(true);
    try {
      // Fazer upload da imagem
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${fileName}`;

      // Remover avatar anterior se existir
      if (avatarUrl) {
        const oldAvatarPath = avatarUrl.split('/').pop();
        if (oldAvatarPath) {
          await supabase.storage.from('avatars').remove([oldAvatarPath]);
        }
      }

      // Upload do novo avatar
      const {
        error: uploadError
      } = await supabase.storage.from('avatars').upload(filePath, file);
      if (uploadError) throw uploadError;

      // Obter URL pública
      const {
        data: publicUrlData
      } = supabase.storage.from('avatars').getPublicUrl(filePath);
      const newAvatarUrl = publicUrlData.publicUrl;

      // Atualizar URL no perfil
      const {
        error: updateError
      } = await supabase.from('profiles').update({
        avatar_url: newAvatarUrl
      }).eq('id', user.id);
      if (updateError) throw updateError;
      setAvatarUrl(newAvatarUrl);
      toast("Avatar atualizado", {
        description: "Sua foto de perfil foi atualizada com sucesso."
      });
    } catch (error: any) {
      toast.error("Erro ao fazer upload", {
        description: error.message || "Ocorreu um erro ao fazer upload da imagem."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para remover avatar
  const handleRemoveAvatar = async () => {
    if (!user || !avatarUrl) return;
    setIsLoading(true);
    try {
      // Extrair o nome do arquivo da URL
      const fileName = avatarUrl.split('/').pop();
      if (fileName) {
        // Remover arquivo do storage
        await supabase.storage.from('avatars').remove([fileName]);
      }

      // Atualizar perfil para remover referência ao avatar
      const {
        error
      } = await supabase.from('profiles').update({
        avatar_url: null
      }).eq('id', user.id);
      if (error) throw error;
      setAvatarUrl(null);
      toast("Avatar removido", {
        description: "Sua foto de perfil foi removida com sucesso."
      });
    } catch (error: any) {
      toast.error("Erro ao remover avatar", {
        description: error.message || "Ocorreu um erro ao remover a imagem."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar o perfil
  const onSubmit = async (data: ProfileFormValues) => {
    if (!user) return;
    setIsLoading(true);
    try {
      // Atualizar o nome no perfil
      const {
        error: profileError
      } = await supabase.from("profiles").update({
        name: data.name
      }).eq("id", user.id);
      if (profileError) throw profileError;

      // Se uma nova senha foi fornecida, atualizá-la
      if (data.password && data.password.length > 0) {
        const {
          error: passwordError
        } = await supabase.auth.updateUser({
          password: data.password
        });
        if (passwordError) throw passwordError;
      }
      toast("Perfil atualizado", {
        description: "Suas informações foram atualizadas com sucesso."
      });
      onOpenChange(false); // Fechar o modal após salvar

      // Recarregar a página para atualizar as informações do usuário
      window.location.reload();
    } catch (error: any) {
      toast.error("Erro ao atualizar perfil", {
        description: error.message || "Ocorreu um erro ao atualizar o perfil."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Gerar iniciais do nome para o avatar
  const getInitials = (name: string) => {
    return name.split(" ").map(n => n[0]).join("").toUpperCase().substring(0, 2);
  };
  return <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Editar Perfil</DialogTitle>
          <DialogDescription>
            Atualize as informações do seu perfil aqui.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center mb-4 mt-2">
          <Avatar className="h-24 w-24 mb-3">
            {avatarUrl ? <AvatarImage src={avatarUrl} alt="Foto de perfil" /> : <AvatarFallback className="text-lg bg-gray-200">
                {profile?.name ? getInitials(profile.name) : "OU"}
              </AvatarFallback>}
          </Avatar>

          <div className="flex gap-2 mt-2">
            <Button type="button" variant="outline" size="sm" className="flex items-center gap-1" onClick={handleAvatarChange} disabled={isLoading}>
              <Image className="h-4 w-4" />
              Alterar Foto
            </Button>
            <Button type="button" variant="outline" size="sm" className="flex items-center gap-1" onClick={handleRemoveAvatar} disabled={isLoading || !avatarUrl}>
              <Trash2 className="h-4 w-4" />
              Remover
            </Button>

            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*" style={{
            display: 'none'
          }} />
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField control={form.control} name="name" render={({
            field
          }) => <FormItem>
                  <FormLabel required>Nome</FormLabel>
                  <FormControl>
                    <Input placeholder="Seu nome" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <FormField control={form.control} name="password" render={({
            field
          }) => <FormItem>
                  <FormLabel>Nova Senha (opcional)</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="Deixe em branco para manter a senha atual" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <div className="text-xs text-gray-500 mb-4">
              <span className="text-destructive">*</span> Campos obrigatórios
            </div>

            <DialogFooter className="pt-4 modal-footer">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading} className="my-0">
                {isLoading ? "Salvando..." : "Salvar Alterações"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>;
};
export default ProfileEditModal;