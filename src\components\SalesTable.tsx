/**
 * @file SalesTable.tsx
 * @description Componente de tabela de vendas que exibe, filtra e permite gerenciar
 * as vendas do sistema. Inclui funcionalidades de busca, filtragem por status,
 * paginação, edição, exclusão e visualização de comprovantes de pagamento.
 */

import React, { useState, useEffect } from "react";
import { Search, ShoppingBag, FileText, Check, X, Clock, Edit, Filter, Trash2, Eye, Download } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn, formatCurrency } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import EditSaleForm from "./EditSaleForm";
import NewSaleForm from "./NewSaleForm";
import { toast } from "sonner";
import { Sale } from "@/types";
import { useSales } from "@/hooks/use-sales";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import DataTablePagination from "@/components/ui/data-table-pagination";
import { paginateData } from "@/lib/pagination";

/**
 * Props para o componente SalesTable
 *
 * @interface SalesTableProps
 */
interface SalesTableProps {
  /** Nome do usuário atual para filtrar vendas por vendedor */
  currentUser: string;
  /** Indica se o usuário atual é administrador */
  isAdmin: boolean;
  /** Classes CSS opcionais para estilização adicional */
  className?: string;
}

/**
 * Componente de tabela de vendas com funcionalidades de filtragem, paginação e gestão
 *
 * @component
 * @param {SalesTableProps} props - Props do componente
 * @returns {JSX.Element} Componente SalesTable renderizado
 */
const SalesTable: React.FC<SalesTableProps> = ({
  currentUser,
  isAdmin,
  className,
}) => {
  // Estados para controle de filtros e ações
  const [searchTerm, setSearchTerm] = useState(""); // Termo de busca
  const [statusFilter, setStatusFilter] = useState<string | null>(null); // Filtro por status
  const [editingSale, setEditingSale] = useState<Sale | null>(null); // Venda sendo editada
  const [isAddingNewSale, setIsAddingNewSale] = useState(false); // Controla diálogo de nova venda
  const [viewProofSale, setViewProofSale] = useState<Sale | null>(null); // Venda com comprovante sendo visualizado
  const [saleToDelete, setSaleToDelete] = useState<string | null>(null); // ID da venda a ser excluída

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1); // Página atual
  const [pageSize, setPageSize] = useState(10); // Tamanho da página

  /**
   * Obtém dados e funções relacionadas a vendas do hook personalizado
   */
  const {
    sales, // Lista de todas as vendas
    isLoading, // Estado de carregamento
    productVariants, // Variações de produtos disponíveis
    statuses, // Status possíveis para vendas
    paymentMethods, // Métodos de pagamento disponíveis
    sellers, // Lista de vendedores
    deliveryPersons, // Lista de entregadores
    addSale, // Função para adicionar uma nova venda
    updateSale, // Função para atualizar uma venda existente
    deleteSale, // Função para excluir uma venda
    getPaymentProofUrl // Função para obter URL do comprovante de pagamento
  } = useSales();

  /**
   * Filtra vendas com base no papel do usuário, termo de busca e filtro de status
   */
  const filteredSales = sales.filter(sale => {
    // Admin pode ver todas as vendas, não-admin só pode ver suas próprias vendas
    const userFilter = isAdmin ? true : sale.seller?.name === currentUser;

    // Aplica filtro de busca por nome do cliente, produto ou número da venda
    const searchFilter =
      searchTerm === "" ||
      sale.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.product_variant?.product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.sale_number.toString().includes(searchTerm);

    // Aplica filtro de status
    const statusFilterCheck =
      statusFilter === null || sale.status_id === statusFilter;

    // Combina todos os filtros
    return userFilter && searchFilter && statusFilterCheck;
  });

  /**
   * Efeito para resetar para a primeira página quando os filtros mudarem
   * Evita páginas vazias quando os filtros reduzem significativamente o número de resultados
   */
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  /**
   * Aplica paginação aos dados filtrados
   * Divide os dados em páginas com base no tamanho da página e página atual
   */
  const { paginatedData, totalItems, totalPages } = paginateData(
    filteredSales,
    currentPage,
    pageSize
  );

  /**
   * Manipula a mudança de página na paginação
   *
   * @param {number} page - Número da página selecionada
   */
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  /**
   * Manipula a mudança no tamanho da página
   *
   * @param {number} newPageSize - Novo tamanho de página selecionado
   */
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho da página
  };

  /**
   * Gera um badge visual para o status da venda
   * Cada status tem uma cor e ícone específicos para fácil identificação
   *
   * @param {string} status - Nome do status da venda
   * @returns {JSX.Element | null} Badge visual para o status ou null se não reconhecido
   */
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Realizado":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
            <Check className="h-3 w-3 mr-1" />
            Realizado
          </span>
        );
      case "Reservado":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
            <Clock className="h-3 w-3 mr-1" />
            Reservado
          </span>
        );
      case "Cancelado":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
            <X className="h-3 w-3 mr-1" />
            Cancelado
          </span>
        );
      default:
        return null;
    }
  };

  const handleEditSale = (sale: Sale) => {
    setEditingSale(sale);
  };

  const handleSaveEdit = (data: any, proofFile?: File) => {
    if (!editingSale) return;

    updateSale({
      id: editingSale.id,
      updates: data,
      proofFile
    });

    setEditingSale(null);
  };

  const handleAddNewSale = () => {
    setIsAddingNewSale(true);
  };

  const handleSaveNewSale = (data: any, proofFile?: File) => {
    addSale({
      sale: data,
      proofFile
    });

    setIsAddingNewSale(false);
  };

  const handleViewPaymentProof = (sale: Sale) => {
    setViewProofSale(sale);
  };

  const handleDeleteConfirm = () => {
    if (saleToDelete) {
      deleteSale(saleToDelete);
      setSaleToDelete(null);
    }
  };

  const handleDeleteSale = (id: string) => {
    setSaleToDelete(id);
  };

  const downloadProof = (sale: Sale) => {
    if (!sale.payment_proof) {
      toast.error("Não há comprovativo para download");
      return;
    }

    const proofUrl = getPaymentProofUrl(sale.payment_proof);
    if (!proofUrl) {
      toast.error("Erro ao obter URL do comprovativo");
      return;
    }

    window.open(proofUrl, '_blank');
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-8 text-center">
        <ShoppingBag className="h-12 w-12 mx-auto mb-4 text-gray-300 animate-pulse" />
        <p className="text-gray-500">Carregando vendas...</p>
      </div>
    );
  }

  return (
    <div className={cn("bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 animate-scale-in", className)}>
      <div className="p-6 border-b border-gray-100 dark:border-gray-800">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-0">
          <h2 className="text-lg font-semibold flex items-center">
            <ShoppingBag className="h-5 w-5 mr-2 text-oluchys-accent" />
            Controle de Vendas
          </h2>
        </div>

        {/* Layout para dispositivos móveis */}
        <div className="flex flex-col gap-3 sm:hidden">
          {/* Campo de busca */}
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Buscar venda..."
              className="pl-9 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filtro de Status */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1 h-10 w-full justify-between">
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  Status: {statusFilter ? statuses.find(s => s.id === statusFilter)?.name || "Todos" : "Todos"}
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter(null)}>
                Todos
              </DropdownMenuItem>
              {statuses.map(status => (
                <DropdownMenuItem
                  key={status.id}
                  onClick={() => setStatusFilter(status.id)}
                >
                  {status.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Botão Nova Venda */}
          <Button onClick={handleAddNewSale} className="w-full">Nova Venda</Button>

          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2 mt-3">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>
        </div>

        {/* Layout para desktop */}
        <div className="hidden sm:flex sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>

          {/* Controles de busca, filtro e botão */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Buscar venda..."
                className="pl-9 w-[220px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-10">
                  <Filter className="h-4 w-4" />
                  Status: {statusFilter ? statuses.find(s => s.id === statusFilter)?.name || "Todos" : "Todos"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setStatusFilter(null)}>
                  Todos
                </DropdownMenuItem>
                {statuses.map(status => (
                  <DropdownMenuItem
                    key={status.id}
                    onClick={() => setStatusFilter(status.id)}
                  >
                    {status.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button onClick={handleAddNewSale}>Nova Venda</Button>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-800 text-left">
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Venda</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Cliente</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Data</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Produto</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Valor</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Entregador</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Vendedor</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Status</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Comprovativo</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Observação</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
            {paginatedData.map((sale) => (
              <tr
                key={sale.id}
                className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <td className="px-6 py-4 font-medium">#{sale.sale_number}</td>
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium">{sale.customer_name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{sale.customer_contact}</div>
                  </div>
                </td>
                <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
                  {sale.sale_date && format(new Date(sale.sale_date), 'dd/MM/yyyy', { locale: ptBR })}
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div>{sale.product_variant?.product?.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {sale.product_variant?.color?.name}, {sale.product_variant?.size?.name}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium">{formatCurrency(Number(sale.price))}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{sale.payment_method?.name}</div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div>{sale.delivery_person?.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{sale.delivery_person?.phone}</div>
                  </div>
                </td>
                <td className="px-6 py-4 text-gray-500 dark:text-gray-400">{sale.seller?.name}</td>
                <td className="px-6 py-4">{getStatusBadge(sale.status?.name || "")}</td>
                <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
                  {sale.payment_proof ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs px-2 py-1 h-auto"
                      onClick={() => handleViewPaymentProof(sale)}
                    >
                      Ver comprovativo
                    </Button>
                  ) : "-"}
                </td>
                <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
                  {sale.notes ? sale.notes : "-"}
                </td>
                <td className="px-6 py-4">
                  <div className="flex justify-end gap-2">
                    {sale.payment_proof && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        aria-label="Download comprovativo"
                        onClick={() => downloadProof(sale)}
                        title="Download comprovativo"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="Ver detalhes"
                      onClick={() => handleViewPaymentProof(sale)}
                      title="Ver detalhes e comprovativo"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="Editar venda"
                      onClick={() => handleEditSale(sale)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/10"
                      aria-label="Excluir venda"
                      onClick={() => handleDeleteSale(sale.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Componente de paginação */}
        {!isLoading && filteredSales.length > 0 && (
          <DataTablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}

        {filteredSales.length === 0 && (
          <div className="py-12 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
            <ShoppingBag className="h-12 w-12 mb-4 opacity-20" />
            <h3 className="text-lg font-medium mb-1">Nenhuma venda encontrada</h3>
            <p className="text-sm">Tente ajustar os filtros de busca</p>
          </div>
        )}
      </div>

      {/* Edit Sale Dialog */}
      <Dialog open={!!editingSale} onOpenChange={(open) => !open && setEditingSale(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Venda #{editingSale?.sale_number}</DialogTitle>
            <DialogDescription>
              Atualize os detalhes da venda conforme necessário.
            </DialogDescription>
          </DialogHeader>
          {editingSale && (
            <EditSaleForm
              sale={editingSale}
              onSubmit={handleSaveEdit}
              onCancel={() => setEditingSale(null)}
              productVariants={productVariants}
              paymentMethods={paymentMethods}
              statuses={statuses}
              sellers={sellers}
              deliveryPersons={deliveryPersons}
              getPaymentProofUrl={getPaymentProofUrl}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* New Sale Dialog */}
      <Dialog open={isAddingNewSale} onOpenChange={setIsAddingNewSale}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Nova Venda</DialogTitle>
            <DialogDescription>
              Preencha os detalhes da nova venda.
            </DialogDescription>
          </DialogHeader>
          <NewSaleForm
            onSubmit={handleSaveNewSale}
            onCancel={() => setIsAddingNewSale(false)}
            productVariants={productVariants}
            paymentMethods={paymentMethods}
            statuses={statuses}
            sellers={sellers}
            deliveryPersons={deliveryPersons}
          />
        </DialogContent>
      </Dialog>

      {/* View Payment Proof Dialog */}
      <Dialog open={!!viewProofSale} onOpenChange={(open) => !open && setViewProofSale(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes da Venda #{viewProofSale?.sale_number}</DialogTitle>
            <DialogDescription>
              Informações detalhadas e comprovativo de pagamento.
            </DialogDescription>
          </DialogHeader>

          {viewProofSale && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Cliente</h4>
                  <p>{viewProofSale.customer_name}</p>
                  <p className="text-sm text-gray-500">{viewProofSale.customer_contact}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Data</h4>
                  <p>{viewProofSale.sale_date && format(new Date(viewProofSale.sale_date), 'dd/MM/yyyy', { locale: ptBR })}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Produto</h4>
                  <p>{viewProofSale.product_variant?.product?.name}</p>
                  <p className="text-sm text-gray-500">
                    {viewProofSale.product_variant?.color?.name}, {viewProofSale.product_variant?.size?.name}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Pagamento</h4>
                  <p>{formatCurrency(Number(viewProofSale.price))}</p>
                  <p className="text-sm text-gray-500">{viewProofSale.payment_method?.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Entregador</h4>
                  <p>{viewProofSale.delivery_person?.name || '-'}</p>
                  <p className="text-sm text-gray-500">{viewProofSale.delivery_person?.phone || '-'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Vendedor</h4>
                  <p>{viewProofSale.seller?.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p>{viewProofSale.status?.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Observação</h4>
                  <p>{viewProofSale.notes || '-'}</p>
                </div>
              </div>

              {viewProofSale.payment_proof ? (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Comprovativo de Pagamento</h4>
                  <div className="rounded-md overflow-hidden border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <img
                      src={getPaymentProofUrl(viewProofSale.payment_proof) || ""}
                      alt="Comprovativo de pagamento"
                      className="w-full object-contain max-h-[300px]"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                        target.alt = 'Erro ao carregar imagem';
                      }}
                    />
                  </div>
                  <div className="mt-2 flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadProof(viewProofSale)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <p>Nenhum comprovativo anexado</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!saleToDelete} onOpenChange={(open) => !open && setSaleToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta venda? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-red-600 hover:bg-red-700">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SalesTable;
