/**
 * @file Sidebar.tsx
 * @description Componente de barra lateral que fornece navegação principal para o sistema.
 * Exibe links para as diferentes páginas da aplicação, com suporte para modo recolhido/expandido,
 * indicadores de notificação para estoque baixo, e controle de acesso baseado em permissões de usuário.
 */

import React, { useEffect, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { BarChart3, DollarSign, Package, ShoppingBag, Users, Settings, FileText, ChevronLeft, ChevronRight, Banknote } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { fetchLowStockProducts } from "@/services/productService";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/context/SidebarContext";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

/**
 * Props para o componente Sidebar
 *
 * @interface SidebarProps
 */
interface SidebarProps {
  /** Estado de visibilidade da barra lateral em dispositivos móveis */
  isOpen: boolean;
  /** Função opcional para controlar a visibilidade da barra lateral em dispositivos móveis */
  setSidebarOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

/**
 * Props para o componente SidebarItem
 *
 * @interface SidebarItemProps
 */
interface SidebarItemProps {
  /** Ícone a ser exibido ao lado do texto do item */
  icon: React.ElementType;
  /** Texto do item de menu */
  label: string;
  /** Caminho de destino para navegação */
  to: string;
  /** Número opcional para exibir um badge de notificação */
  badgeCount?: number;
  /** Define se o item deve ser exibido apenas para administradores */
  adminOnly?: boolean;
  /** Define se a barra lateral está no modo recolhido */
  isCollapsed?: boolean;
  /** Função de callback a ser executada após a navegação */
  onNavigate?: () => void;
}

/**
 * Componente que representa um item individual na barra lateral
 *
 * @component
 * @param {SidebarItemProps} props - Props do componente
 * @returns {JSX.Element | null} Item da barra lateral ou null se não tiver permissão
 */
const SidebarItem: React.FC<SidebarItemProps> = ({
  icon: Icon,
  label,
  to,
  badgeCount,
  adminOnly = false,
  isCollapsed = false,
  onNavigate,
}) => {
  const location = useLocation();
  const isActive = location.pathname === to; // Verifica se o item está ativo com base na rota atual
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = React.useState(false); // Estado para armazenar se o usuário é admin

  /**
   * Efeito para verificar se o usuário tem permissões de administrador
   * Consulta o perfil do usuário no Supabase e verifica se o papel é 'admin'
   */
  React.useEffect(() => {
    const checkAdminRole = async () => {
      if (!user) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!error && data) {
        setIsAdmin(data.role === 'admin');
      }
    };

    checkAdminRole();
  }, [user]);

  // Se o item for apenas para admin e o usuário não for admin, não renderizar
  if (adminOnly && !isAdmin) return null;

  /**
   * Manipula o clique no item de menu, executando a função de callback se fornecida
   */
  const handleClick = () => {
    if (onNavigate) {
      onNavigate();
    }
  };

  const navLink = (
    <NavLink
      to={to}
      onClick={handleClick}
      className={({ isActive }) =>
        cn(
          "flex items-center py-3 text-sidebar-foreground rounded-lg transition-all duration-200 group",
          isCollapsed ? "justify-center px-2" : "px-4",
          isActive
            ? "bg-sidebar-primary text-sidebar-primary-foreground font-medium"
            : "hover:bg-sidebar-accent"
        )
      }
    >
      <Icon className={cn("h-5 w-5 transition-transform group-hover:scale-110",
        isCollapsed ? "mr-0" : "mr-3",
        isActive ? "text-sidebar-primary-foreground" : "text-sidebar-foreground"
      )} />
      {!isCollapsed && <span className="text-sm">{label}</span>}
      {badgeCount !== undefined && badgeCount > 0 && (
        <div
          className={cn(
            "bg-red-500 text-white dark:bg-red-600 dark:text-white rounded-full px-2 py-0.5 text-xs font-semibold",
            isCollapsed ? "ml-0 absolute top-0 right-0 translate-x-1/3 -translate-y-1/3" : "ml-auto",
            "animate-pulse-badge" // Custom animation class
          )}
        >
          {badgeCount}
        </div>
      )}
    </NavLink>
  );

  // If collapsed, wrap with tooltip
  if (isCollapsed) {
    return (
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div className="relative">{navLink}</div>
        </TooltipTrigger>
        <TooltipContent side="right" className="bg-sidebar-accent text-sidebar-foreground">
          {label}
        </TooltipContent>
      </Tooltip>
    );
  }

  return navLink;
};

/**
 * Componente principal da barra lateral que exibe a navegação do sistema
 *
 * @component
 * @param {SidebarProps} props - Props do componente
 * @returns {JSX.Element} Componente Sidebar renderizado
 *
 * @example
 * // Em App.tsx ou outro componente de layout
 * <Sidebar
 *   isOpen={sidebarOpen}
 *   setSidebarOpen={setSidebarOpen}
 * />
 */
const Sidebar: React.FC<SidebarProps> = ({ isOpen, setSidebarOpen }) => {
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const { isCollapsed, toggleCollapsed } = useSidebar();

  /**
   * Consulta para buscar produtos com estoque baixo
   * Configuração da query com refetch automático e staleTime
   */
  const { data: lowStockProducts = [] } = useQuery({
    queryKey: ['lowStockProducts'],
    queryFn: fetchLowStockProducts,
    refetchInterval: 30000, // Refetch a cada 30 segundos
    staleTime: 10000, // Considerar dados obsoletos após 10 segundos
  });

  /**
   * Configura o listener do Supabase Realtime para a tabela 'product_variants'
   * Atualiza automaticamente a lista de produtos com estoque baixo quando há mudanças
   */
  useEffect(() => {
    // Canal para ouvir alterações na tabela product_variants
    const channel = supabase
      .channel('product-variants-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Ouça todos os eventos (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'product_variants'
        },
        () => {
          // Invalidar a consulta quando houver alterações na tabela
          queryClient.invalidateQueries({ queryKey: ['lowStockProducts'] });
        }
      )
      .subscribe();

    // Cleanup: remover o canal quando o componente for desmontado
    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  /**
   * Função para fechar a barra lateral em dispositivos móveis após a navegação
   * Melhora a experiência do usuário em telas pequenas
   */
  const handleMobileNavigation = () => {
    if (isMobile && setSidebarOpen) {
      setSidebarOpen(false);
    }
  };

  const lowStockCount = lowStockProducts.length;

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-20 bg-sidebar pt-16 border-r border-gray-200 dark:border-gray-800 shadow-sm flex flex-col transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-64",
        isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}
    >
      <div className={cn("flex-1 overflow-y-auto space-y-1", isCollapsed ? "px-2 pt-2" : "p-4")}>
        <SidebarItem
          icon={BarChart3}
          label="Dashboard"
          to="/"
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={Banknote}
          label="Fluxo de Caixa"
          to="/cashflow"
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={Package}
          label="Estoque"
          to="/inventory"
          badgeCount={lowStockCount}
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={ShoppingBag}
          label="Vendas"
          to="/sales"
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={Users}
          label="Usuários"
          to="/users"
          adminOnly={true}
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={Settings}
          label="Configurações"
          to="/settings"
          adminOnly={true}
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
        <SidebarItem
          icon={FileText}
          label="Relatórios"
          to="/reports"
          adminOnly={true}
          isCollapsed={isCollapsed}
          onNavigate={handleMobileNavigation}
        />
      </div>

      <div className={cn("border-t border-gray-200 dark:border-gray-800", isCollapsed ? "p-2" : "p-4")}>
        {!isCollapsed ? (
          <div className="px-4 py-3 rounded-lg bg-sidebar-accent">
            <h4 className="text-xs font-medium uppercase text-sidebar-foreground/60 mb-2">
              Sistema Oluchys
            </h4>
            <p className="text-xs text-sidebar-foreground/80">
              Versão 1.0.0
            </p>
          </div>
        ) : (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex justify-center py-3 rounded-lg bg-sidebar-accent">
                <span className="text-xs font-bold">1.0</span>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="bg-sidebar-accent text-sidebar-foreground">
              Sistema Oluchys - Versão 1.0.0
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </aside>
  );
};

export default Sidebar;
