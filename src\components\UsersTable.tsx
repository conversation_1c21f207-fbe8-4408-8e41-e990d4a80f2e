
import React, { useState, useCallback, useEffect } from "react";
import { Search, User } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { User as UserType } from "../pages/Users";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import EditUserForm from "./EditUserForm";
import { toast } from "sonner";
import { updateUser, toggleUserActive, deleteUser, checkAdminsBeforeDelete } from "@/services/userService";
import UserRow from "./users/UserRow";
import EmptyState from "./users/EmptyState";
import DeleteUserDialog from "./users/DeleteUserDialog";
import { debounce } from "lodash";
import DataTablePagination from "@/components/ui/data-table-pagination";
import { paginateData } from "@/lib/pagination";

interface UsersTableProps {
  users: UserType[];
  className?: string;
  onRefresh: () => Promise<void>;
}

const UsersTable: React.FC<UsersTableProps> = ({
  users,
  className,
  onRefresh
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([]);
  const [editingUser, setEditingUser] = useState<UserType | null>(null);
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [loading, setLoading] = useState(false);

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Implementar debounce na busca
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      const filtered = users.filter(user => {
        return (
          term === "" ||
          user.name.toLowerCase().includes(term.toLowerCase()) ||
          user.email.toLowerCase().includes(term.toLowerCase()) ||
          user.phone?.includes(term)
        );
      });
      setFilteredUsers(filtered);
    }, 300),
    [users]
  );

  // Atualizar usuários filtrados quando os usuários mudarem
  React.useEffect(() => {
    setFilteredUsers(users);
    if (searchTerm) {
      debouncedSearch(searchTerm);
    }
  }, [users, searchTerm, debouncedSearch]);

  // Resetar para a primeira página quando os filtros mudarem
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filteredUsers]);

  // Aplicar paginação aos dados filtrados
  const paginationData = paginateData(
    filteredUsers,
    currentPage,
    pageSize
  );
  const { paginatedData, totalItems, totalPages } = paginationData;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho da página
  };

  // Lidar com a alteração do termo de busca
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    debouncedSearch(term);
  };

  const handleEditUser = (user: UserType) => {
    setEditingUser(user);
  };

  const handleSaveEdit = async (userData: any) => {
    if (loading) return;

    try {
      setLoading(true);
      await updateUser(userData.id, userData);
      toast.success("Usuário atualizado com sucesso!");
      setEditingUser(null);
      onRefresh();
    } catch (error: any) {
      console.error("Erro ao atualizar usuário:", error);
      toast.error(`Erro ao atualizar usuário: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (user: UserType) => {
    if (loading) return;

    try {
      setLoading(true);

      if (user.active && user.role === 'admin') {
        const hasOtherAdmins = await checkAdminsBeforeDelete(user.id);
        if (!hasOtherAdmins) {
          toast.error("Não é possível desativar o último administrador ativo.");
          return;
        }
      }

      await toggleUserActive(user.id, !user.active);
      toast.success(`Usuário ${user.active ? 'desativado' : 'ativado'} com sucesso!`);
      onRefresh();
    } catch (error: any) {
      console.error("Erro ao alterar status do usuário:", error);
      toast.error(`Erro: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!userToDelete || loading) return;

    try {
      setLoading(true);
      await deleteUser(userToDelete.id);
      toast.success("Usuário removido com sucesso!");
      setUserToDelete(null);
      setConfirmDelete(false);
      onRefresh();
    } catch (error: any) {
      console.error("Erro ao excluir usuário:", error);
      toast.error(`Erro: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = (user: UserType) => {
    setUserToDelete(user);
    setConfirmDelete(true);
  };

  // Adicionar tratamento de erro para depuração
  try {
    return (
      <div className={cn("bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 animate-scale-in", className)}>
      <div className="p-6 border-b border-gray-100 dark:border-gray-800">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-0">
          <h2 className="text-lg font-semibold flex items-center">
            <User className="h-5 w-5 mr-2 text-primary" />
            Lista de Usuários
          </h2>
        </div>

        {/* Layout para dispositivos móveis */}
        <div className="flex flex-col gap-3 sm:hidden">
          {/* Campo de busca */}
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Buscar usuário..."
              className="pl-9 w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>

          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2 mt-3">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>
        </div>

        {/* Layout para desktop */}
        <div className="hidden sm:flex sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          {/* Seletor de registros por página */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="30">30</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
          </div>

          {/* Campo de busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Buscar usuário..."
              className="pl-9 w-[220px] h-10"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-800 text-left">
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Nome</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Email</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Telefone</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Função</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Status</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Criado em</th>
              <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
            {paginatedData.map((user) => (
              <UserRow
                key={user.id}
                user={user}
                onEdit={handleEditUser}
                onToggleActive={handleToggleActive}
                onDelete={handleDeleteUser}
              />
            ))}
          </tbody>
        </table>

        {filteredUsers.length === 0 && <EmptyState />}

        {/* Componente de paginação */}
        {filteredUsers.length > 0 && (
          <DataTablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}
      </div>

      <Dialog open={!!editingUser} onOpenChange={(open) => !open && setEditingUser(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Usuário</DialogTitle>
            <DialogDescription>
              Atualize os detalhes do usuário conforme necessário.
            </DialogDescription>
          </DialogHeader>
          {editingUser && (
            <EditUserForm
              user={editingUser}
              onSubmit={handleSaveEdit}
              onCancel={() => setEditingUser(null)}
              loading={loading}
            />
          )}
        </DialogContent>
      </Dialog>

      <DeleteUserDialog
        user={userToDelete}
        open={confirmDelete}
        loading={loading}
        onOpenChange={setConfirmDelete}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
  } catch (error) {
    console.error("Erro ao renderizar UsersTable:", error);
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
        <h3 className="text-lg font-medium">Erro ao renderizar tabela de usuários</h3>
        <p>Ocorreu um erro ao renderizar a tabela. Por favor, tente novamente mais tarde.</p>
        <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">{error instanceof Error ? error.message : String(error)}</pre>
      </div>
    );
  }
};

export default UsersTable;
