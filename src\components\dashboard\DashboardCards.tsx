
import React from "react";
import { Package, BarChart3, Users, Banknote } from "lucide-react";
import DashboardCard from "@/components/DashboardCard";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";

interface DashboardCardsProps {
  isLoading: boolean;
  dashboardData?: {
    stockTotal: number;
    todaySalesTotal: number;
    weeklySalesTotal: number;
    customersServed: number;
    stockTrend: { value: number; isPositive: boolean };
    todayTrend: { value: number; isPositive: boolean };
    weeklyTrend: { value: number; isPositive: boolean };
    customerTrend: { value: number; isPositive: boolean };
  };
}

const DashboardCards: React.FC<DashboardCardsProps> = ({ isLoading, dashboardData }) => {
  const formatCurrencyValue = (value: number): string => {
    return formatCurrency(value);
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <DashboardCard
        title="Total em Estoque"
        value={`${dashboardData?.stockTotal || 0} itens`}
        icon={<Package className="h-6 w-6" />}
        trend={dashboardData?.stockTrend}
      />

      <DashboardCard
        title="Vendas de Hoje"
        value={formatCurrencyValue(dashboardData?.todaySalesTotal || 0)}
        icon={<Banknote className="h-6 w-6" />}
        trend={dashboardData?.todayTrend}
      />

      <DashboardCard
        title="Vendas da Semana"
        value={formatCurrencyValue(dashboardData?.weeklySalesTotal || 0)}
        icon={<BarChart3 className="h-6 w-6" />}
        trend={dashboardData?.weeklyTrend}
      />

      <DashboardCard
        title="Clientes Atendidos"
        value={dashboardData?.customersServed || 0}
        icon={<Users className="h-6 w-6" />}
        trend={dashboardData?.customerTrend}
      />
    </div>
  );
};

export default DashboardCards;
