
import React from "react";
import DashboardChart from "@/components/DashboardChart";
import { Skeleton } from "@/components/ui/skeleton";
import StockAlertsCard from "@/components/dashboard/StockAlertsCard";

interface DashboardChartsProps {
  isLoading: boolean;
  dashboardData?: {
    salesByDay: { name: string; value: number }[];
    stockByCategory: { name: string; value: number }[];
    lowStockProducts: any[];
  };
  onViewInventory: () => void;
}

const DashboardCharts: React.FC<DashboardChartsProps> = ({
  isLoading,
  dashboardData,
  onViewInventory
}) => {
  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {isLoading ? (
          <>
            <Skeleton className="h-[350px] w-full lg:col-span-2" />
            <Skeleton className="h-[350px] w-full" />
          </>
        ) : (
          <>
            <DashboardChart
              title="Vendas da Semana"
              subtitle="Valor total de vendas por dia"
              data={dashboardData?.salesByDay || []}
              className="lg:col-span-2"
            />

            <StockAlertsCard
              isLoading={isLoading}
              lowStockProducts={dashboardData?.lowStockProducts}
              onViewInventory={onViewInventory}
              buttonTooltip="Ir para estoque"
            />
          </>
        )}
      </div>

      {isLoading ? (
        <Skeleton className="h-[350px] w-full" />
      ) : (
        <DashboardChart
          title="Visão Geral do Estoque"
          subtitle="Quantidade por categoria de produto"
          data={dashboardData?.stockByCategory || []}
        />
      )}
    </>
  );
};

export default DashboardCharts;
