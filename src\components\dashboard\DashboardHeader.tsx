
import React from "react";
import { ShoppingBag } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface DashboardHeaderProps {
  onNewSaleClick: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  onNewSaleClick,
}) => {
  return (
    <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
      <div>
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <p className="text-gray-500 dark:text-gray-400 mt-1">
          Visão geral do sistema Oluchys
        </p>
      </div>
      
      <div className="flex flex-wrap gap-3">
        <Button size="sm" className="gap-1" onClick={onNewSaleClick}>
          <ShoppingBag className="h-4 w-4" />
          Nova Venda
        </Button>
      </div>
    </div>
  );
};

export default DashboardHeader;
