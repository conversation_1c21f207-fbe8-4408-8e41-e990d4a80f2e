
import React from "react";
import { ArrowUpRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import NotificationBadge from "@/components/NotificationBadge";
import { Skeleton } from "@/components/ui/skeleton";

interface NotificationsCardProps {
  isLoading: boolean;
  notifications?: { id: number; message: string }[];
  onNotificationsClick: () => void;
}

const NotificationsCard: React.FC<NotificationsCardProps> = ({ 
  isLoading, 
  notifications = [],
  onNotificationsClick
}) => {
  if (isLoading) {
    return <Skeleton className="h-[350px] w-full" />;
  }

  return (
    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Notificações</h3>
        <div className="relative">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={onNotificationsClick}
          >
            <ArrowUpRight className="h-4 w-4" />
          </Button>
          <NotificationBadge count={notifications.length || 0} />
        </div>
      </div>
      
      {notifications && notifications.length > 0 ? (
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div 
              key={notification.id}
              className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700"
            >
              <p className="text-sm">{notification.message}</p>
            </div>
          ))}
        </div>
      ) : (
        <div className="py-8 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
          <p className="text-sm">Nenhuma notificação no momento</p>
        </div>
      )}
    </div>
  );
};

export default NotificationsCard;
