
import React from "react";
import { ArrowUpRight, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { ProductVariant } from "@/types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface StockAlertsCardProps {
  isLoading: boolean;
  lowStockProducts?: (ProductVariant & {
    product: {
      name: string;
      category: {
        name: string;
      };
    };
    color: {
      name: string;
    };
    size: {
      name: string;
    };
  })[];
  onViewInventory?: () => void;
  buttonTooltip?: string;
}

const StockAlertsCard: React.FC<StockAlertsCardProps> = ({
  isLoading,
  lowStockProducts = [],
  onViewInventory,
  buttonTooltip = "Ver inventário"
}) => {
  const MAX_VISIBLE_ITEMS = 5;

  if (isLoading) {
    return <Skeleton className="h-[800px] w-full" />;
  }

  const getStockCriticality = (quantity: number) => {
    if (quantity <= 2) return "bg-red-100 dark:bg-red-950 border-red-200 dark:border-red-800";
    return "bg-orange-100 dark:bg-orange-950 border-orange-200 dark:border-orange-800";
  };

  const getStockTextColor = (quantity: number) => {
    if (quantity <= 2) return "text-red-700 dark:text-red-400";
    return "text-orange-700 dark:text-orange-400";
  };

  const getAlertIconColor = (quantity: number) => {
    if (quantity <= 2) return "text-red-700 dark:text-red-400";
    return "text-orange-700 dark:text-orange-400";
  };

  const visibleProducts = lowStockProducts.slice(0, MAX_VISIBLE_ITEMS);
  const remainingCount = Math.max(0, lowStockProducts.length - MAX_VISIBLE_ITEMS);

  return (
    <Card className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6 h-[410px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Alertas de Estoque</h3>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={onViewInventory}
              >
                <ArrowUpRight className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{buttonTooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {lowStockProducts.length > 0 ? (
        <>
          <ScrollArea className="flex-1 -mx-2 px-2">
            <div className="space-y-3">
              {visibleProducts.map((product) => (
                <div
                  key={product.id}
                  className={`p-3 rounded-lg border ${getStockCriticality(product.quantity)}`}
                >
                  <div className="flex justify-between items-start gap-2">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{product.product.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {product.color.name} • {product.size.name}
                      </p>
                    </div>
                    <div className={`text-sm font-medium ${getStockTextColor(product.quantity)} flex items-center gap-1`}>
                      <AlertTriangle className={`h-4 w-4 ${getAlertIconColor(product.quantity)}`} />
                      {product.quantity} un.
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          {remainingCount > 0 && (
            <div className="mt-3 pt-2 border-t border-gray-100 dark:border-gray-800">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                +{remainingCount} outros produtos com estoque baixo
              </p>
            </div>
          )}
        </>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500 dark:text-gray-400">
          <p className="text-sm">Todos os produtos estão com estoque adequado</p>
        </div>
      )}
    </Card>
  );
};

export default StockAlertsCard;
