/**
 * @file MetricCard.tsx
 * @description Componente de card para exibição de métricas com suporte a ícones,
 * descrições e mini-gráficos (sparklines) para visualização de tendências.
 */

import React from "react"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"

interface MetricCardProps {
  title: string
  value: string | number
  description?: string | React.ReactNode
  icon?: LucideIcon
  className?: string
  iconColor?: string
}

/**
 * Componente de card para exibição de métricas com suporte a ícones,
 * descrições e mini-gráficos (sparklines) para visualização de tendências
 *
 * @component
 * @param {MetricCardProps} props - Propriedades do componente
 * @returns {JSX.Element} Card de métrica com visual aprimorado
 */
export function MetricCard({
  title,
  value,
  description,
  icon: Icon,
  className,
  iconColor = "text-blue-500"
}: MetricCardProps) {
  return (
    <div className={cn(
      "relative overflow-hidden rounded-xl bg-white dark:bg-gray-900 shadow-card hover:shadow-card-hover transition-all duration-300 border border-gray-100 dark:border-gray-800 min-h-[160px] flex flex-col",
      className
    )}>
      <div className="p-6 flex-1 flex flex-col">
        <div className="flex items-start justify-between mb-2">
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              {title}
            </p>
            <div className="text-xl font-bold">{value}</div>
            {description && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 line-clamp-2">
                {description}
              </p>
            )}
          </div>
          {Icon && <Icon className={`h-6 w-6 ${iconColor} flex-shrink-0 ml-4`} />}
        </div>

        {/* Sparklines removidos conforme solicitação do cliente */}
      </div>
      {/* Decorative circle */}
      <div className="absolute -bottom-4 -right-4 w-24 h-24 rounded-full bg-gradient-to-br from-blue-500/5 to-blue-500/10" />
    </div>
  )
}
