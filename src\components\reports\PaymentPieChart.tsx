/**
 * @file PaymentPieChart.tsx
 * @description Componente que exibe um gráfico de pizza para visualização da distribuição
 * de vendas por método de pagamento. Utiliza a biblioteca Recharts para renderização.
 */

import React from "react";
import { Pie<PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts";
import { cn } from "@/lib/utils";

// Cores temáticas para os métodos de pagamento
const PAYMENT_COLORS = [
  "#1E40AF", // Azul escuro
  "#3B82F6", // Azul médio
  "#60A5FA", // Azul claro
  "#93C5FD", // Azul muito claro
  "#10B981", // Verde
  "#F59E0B", // Amarelo
  "#EF4444", // Vermelho
  "#8B5CF6", // Roxo
  "#EC4899", // Rosa
  "#6B7280", // Cinza
];

interface PaymentData {
  id: string;
  name: string;
  totalSales: number;
  totalAmount: number;
}

interface PaymentPieChartProps {
  data: PaymentData[];
  className?: string;
  valueType?: "amount" | "sales";
  title?: string;
}

/**
 * Componente que exibe um gráfico de pizza para visualização da distribuição
 * de vendas por método de pagamento
 * 
 * @component
 * @param {PaymentPieChartProps} props - Propriedades do componente
 * @returns {JSX.Element} Gráfico de pizza com dados de pagamento
 */
const PaymentPieChart: React.FC<PaymentPieChartProps> = ({
  data,
  className,
  valueType = "amount",
  title = "Distribuição por Método de Pagamento"
}) => {
  // Preparar dados para o gráfico
  const chartData = data.map((item) => ({
    name: item.name,
    value: valueType === "amount" ? item.totalAmount : item.totalSales,
  }));

  // Calcular o total para percentuais
  const total = chartData.reduce((sum, item) => sum + item.value, 0);

  // Formatar valores para o tooltip
  const formatValue = (value: number) => {
    if (valueType === "amount") {
      return new Intl.NumberFormat('pt-AO', {
        style: 'currency',
        currency: 'AOA',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    }
    return value.toString();
  };

  // Formatar percentual para o tooltip
  const formatPercent = (value: number) => {
    return `${((value / total) * 100).toFixed(1)}%`;
  };

  // Renderizador customizado para o tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 rounded-lg shadow-md border border-gray-200">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">
            {valueType === "amount" ? "Valor: " : "Vendas: "}
            <span className="font-medium">{formatValue(data.value)}</span>
          </p>
          <p className="text-sm">
            Percentual: <span className="font-medium">{formatPercent(data.value)}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  // Renderizador customizado para o rótulo
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    // Só mostra o rótulo se o percentual for maior que 5%
    if (percent < 0.05) return null;

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor="middle"
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={PAYMENT_COLORS[index % PAYMENT_COLORS.length]} 
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              layout="horizontal" 
              verticalAlign="bottom" 
              align="center"
              formatter={(value) => <span className="text-sm">{value}</span>}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PaymentPieChart;
