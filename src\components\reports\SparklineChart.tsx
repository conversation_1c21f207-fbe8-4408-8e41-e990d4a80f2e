/**
 * @file SparklineChart.tsx
 * @description Componente que exibe um mini-gráfico de linha (sparkline) para visualização
 * de tendências em espaços compactos como cards de métricas.
 */

import React from "react";
import { LineChart, Line, ResponsiveContainer } from "recharts";
import { cn } from "@/lib/utils";

interface SparklineData {
  name: string;
  value: number;
}

interface SparklineChartProps {
  data: SparklineData[];
  color?: string;
  className?: string;
  height?: number;
}

/**
 * Componente que exibe um mini-gráfico de linha (sparkline) para visualização
 * de tendências em espaços compactos
 *
 * @component
 * @param {SparklineChartProps} props - Propriedades do componente
 * @returns {JSX.Element} Mini-gráfico de linha
 */
const SparklineChart: React.FC<SparklineChartProps> = ({
  data,
  color = "hsl(var(--oluchys-accent))",
  className,
  height = 40
}) => {
  // Verificar se há dados suficientes para renderizar o gráfico
  if (!data || data.length < 2) {
    return <div className={cn(`h-[${height}px]`, className)} />;
  }

  // Determinar se a tendência é positiva (último valor maior que o primeiro)
  const isPositive = data[data.length - 1].value >= data[0].value;

  // Usar cor baseada na tendência se não for especificada
  const lineColor = color || (isPositive ? "#10B981" : "#EF4444");

  return (
    <div className={cn("w-full", className)}>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey="value"
            stroke={lineColor}
            strokeWidth={2}
            dot={{ r: 0 }}
            activeDot={{ r: 4 }}
            isAnimationActive={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default SparklineChart;
