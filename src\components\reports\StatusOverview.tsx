/**
 * @file StatusOverview.tsx
 * @description Componente que exibe um resumo visual do status das vendas,
 * mostrando a quantidade de vendas realizadas, reservadas e canceladas.
 * Utiliza ícones coloridos para facilitar a identificação visual rápida.
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CircleCheck, CircleMinus, CirclePause } from "lucide-react"

/**
 * Interface que define as propriedades do componente StatusOverview
 */
interface StatusOverviewProps {
  /** Número de vendas realizadas/concluídas */
  realizadas: number
  /** Número de vendas em estado de reserva */
  reservadas: number
  /** Número de vendas canceladas */
  canceladas: number
}

/**
 * Componente que exibe um resumo visual do status das vendas
 *
 * @component
 * @example
 * // Exemplo de uso
 * <StatusOverview
 *   realizadas={125}
 *   reservadas={42}
 *   canceladas={18}
 * />
 *
 * @param {StatusOverviewProps} props - Propriedades do componente
 * @returns {JSX.Element} Card com resumo visual dos status de vendas
 */
export function StatusOverview({ realizadas, reservadas, canceladas }: StatusOverviewProps) {
  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Status das Vendas</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-4 items-center justify-center md:justify-start">
          {/* Indicador de vendas realizadas com ícone verde */}
          <div className="flex items-center gap-2">
            <CircleCheck className="h-4 w-4 text-green-500" />
            <span className="text-sm">Realizadas: {realizadas}</span>
          </div>
          {/* Indicador de vendas reservadas com ícone amarelo */}
          <div className="flex items-center gap-2">
            <CirclePause className="h-4 w-4 text-yellow-500" />
            <span className="text-sm">Reservadas: {reservadas}</span>
          </div>
          {/* Indicador de vendas canceladas com ícone vermelho */}
          <div className="flex items-center gap-2">
            <CircleMinus className="h-4 w-4 text-red-500" />
            <span className="text-sm">Canceladas: {canceladas}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
