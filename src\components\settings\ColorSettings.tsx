import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchColors, addColor, updateColor, deleteColor, Color } from "@/services/colorService";
const ColorSettings = () => {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState<Color | null>(null);
  const [newColor, setNewColor] = useState({
    name: "",
    hexCode: "#000000"
  });
  const {
    data: colors = [],
    isLoading
  } = useQuery({
    queryKey: ['colors'],
    queryFn: fetchColors
  });
  const addColorMutation = useMutation({
    mutationFn: addColor,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['colors']
      });
      setNewColor({
        name: "",
        hexCode: "#000000"
      });
      setIsAddDialogOpen(false);
    }
  });
  const updateColorMutation = useMutation({
    mutationFn: updateColor,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['colors']
      });
      setIsEditDialogOpen(false);
    }
  });
  const deleteColorMutation = useMutation({
    mutationFn: deleteColor,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['colors']
      });
      setIsDeleteDialogOpen(false);
    }
  });
  const handleAddColor = () => {
    addColorMutation.mutate(newColor);
  };
  const handleEditColor = () => {
    if (selectedColor) {
      updateColorMutation.mutate(selectedColor);
    }
  };
  const handleDeleteColor = () => {
    if (selectedColor) {
      deleteColorMutation.mutate(selectedColor.id);
    }
  };
  return <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Cores</h2>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Adicionar
        </Button>
      </div>
      
      {isLoading ? <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
        </div> : <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Cor</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {colors.map(color => <TableRow key={color.id}>
                <TableCell className="font-medium">{color.name}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full mr-2 border border-gray-200" style={{
                backgroundColor: color.hexCode
              }}></div>
                    {color.hexCode}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" onClick={() => {
              setSelectedColor(color);
              setIsEditDialogOpen(true);
            }}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700" onClick={() => {
              setSelectedColor(color);
              setIsDeleteDialogOpen(true);
            }}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>)}
            
            {colors.length === 0 && <TableRow>
                <TableCell colSpan={3} className="text-center py-4 text-gray-500">
                  Nenhuma cor cadastrada
                </TableCell>
              </TableRow>}
          </TableBody>
        </Table>}
      
      {/* Add Color Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Cor</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="colorName">Nome da Cor</label>
              <Input id="colorName" value={newColor.name} onChange={e => setNewColor({
              ...newColor,
              name: e.target.value
            })} placeholder="Ex: Azul" />
            </div>
            <div className="grid gap-2">
              <label htmlFor="colorHex">Código Hexadecimal</label>
              <div className="flex items-center gap-2">
                <Input id="colorHex" value={newColor.hexCode} onChange={e => setNewColor({
                ...newColor,
                hexCode: e.target.value
              })} placeholder="Ex: #0000FF" />
                <input type="color" value={newColor.hexCode} onChange={e => setNewColor({
                ...newColor,
                hexCode: e.target.value
              })} className="w-10 h-10 p-1 border rounded cursor-pointer" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddColor} disabled={addColorMutation.isPending || !newColor.name.trim()} className="my-0">
              {addColorMutation.isPending ? "Adicionando..." : "Adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Color Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Cor</DialogTitle>
          </DialogHeader>
          {selectedColor && <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="editColorName">Nome da Cor</label>
                <Input id="editColorName" value={selectedColor.name} onChange={e => setSelectedColor({
              ...selectedColor,
              name: e.target.value
            })} />
              </div>
              <div className="grid gap-2">
                <label htmlFor="editColorHex">Código Hexadecimal</label>
                <div className="flex items-center gap-2">
                  <Input id="editColorHex" value={selectedColor.hexCode} onChange={e => setSelectedColor({
                ...selectedColor,
                hexCode: e.target.value
              })} />
                  <input type="color" value={selectedColor.hexCode} onChange={e => setSelectedColor({
                ...selectedColor,
                hexCode: e.target.value
              })} className="w-10 h-10 p-1 border rounded cursor-pointer" />
                </div>
              </div>
            </div>}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleEditColor} disabled={updateColorMutation.isPending || !selectedColor?.name.trim()} className="my-0">
              {updateColorMutation.isPending ? "Salvando..." : "Salvar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
          </DialogHeader>
          <p>
            Tem certeza que deseja excluir a cor "{selectedColor?.name}"?
            Esta ação não pode ser desfeita.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancelar</Button>
            <Button variant="destructive" onClick={handleDeleteColor} disabled={deleteColorMutation.isPending}>
              {deleteColorMutation.isPending ? "Excluindo..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>;
};
export default ColorSettings;