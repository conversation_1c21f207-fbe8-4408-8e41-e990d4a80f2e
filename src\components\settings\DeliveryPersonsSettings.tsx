import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Plus, Pencil, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
interface DeliveryPerson {
  id: string;
  name: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}
const DeliveryPersonsSettings = () => {
  const [deliveryPersons, setDeliveryPersons] = useState<DeliveryPerson[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentDeliveryPerson, setCurrentDeliveryPerson] = useState<DeliveryPerson | null>(null);
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  React.useEffect(() => {
    fetchDeliveryPersons();
  }, []);
  const fetchDeliveryPersons = async () => {
    try {
      setLoading(true);
      const {
        data,
        error
      } = await supabase.from("delivery_persons").select("*").order("name");
      if (error) throw error;
      setDeliveryPersons(data);
    } catch (error: any) {
      console.error("Erro ao buscar motoqueiros:", error.message);
      toast.error(`Erro ao carregar motoqueiros: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  const handleOpenDialog = (deliveryPerson?: DeliveryPerson) => {
    if (deliveryPerson) {
      setCurrentDeliveryPerson(deliveryPerson);
      setName(deliveryPerson.name);
      setPhone(deliveryPerson.phone || "");
    } else {
      setCurrentDeliveryPerson(null);
      setName("");
      setPhone("");
    }
    setIsDialogOpen(true);
  };
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setCurrentDeliveryPerson(null);
    setName("");
    setPhone("");
  };
  const handleSave = async () => {
    try {
      if (!name.trim()) {
        toast.error("O nome do motoqueiro é obrigatório");
        return;
      }
      if (currentDeliveryPerson) {
        // Atualização
        const {
          error
        } = await supabase.from("delivery_persons").update({
          name,
          phone: phone || null
        }).eq("id", currentDeliveryPerson.id);
        if (error) throw error;
        toast.success("Motoqueiro atualizado com sucesso");
      } else {
        // Inserção
        const {
          error
        } = await supabase.from("delivery_persons").insert({
          name,
          phone: phone || null
        });
        if (error) throw error;
        toast.success("Motoqueiro adicionado com sucesso");
      }
      handleCloseDialog();
      fetchDeliveryPersons();
    } catch (error: any) {
      console.error("Erro ao salvar motoqueiro:", error.message);
      toast.error(`Erro ao salvar motoqueiro: ${error.message}`);
    }
  };
  const handleDelete = async (id: string) => {
    if (!confirm("Tem certeza que deseja excluir este motoqueiro?")) return;
    try {
      // Verificar se o motoqueiro está sendo usado em alguma venda
      const {
        data: salesData,
        error: salesError
      } = await supabase.from("sales").select("id").eq("delivery_person_id", id).limit(1);
      if (salesError) throw salesError;
      if (salesData && salesData.length > 0) {
        toast.error("Este motoqueiro não pode ser excluído porque está associado a vendas");
        return;
      }

      // Excluir o motoqueiro
      const {
        error
      } = await supabase.from("delivery_persons").delete().eq("id", id);
      if (error) throw error;
      toast.success("Motoqueiro excluído com sucesso");
      fetchDeliveryPersons();
    } catch (error: any) {
      console.error("Erro ao excluir motoqueiro:", error.message);
      toast.error(`Erro ao excluir motoqueiro: ${error.message}`);
    }
  };
  if (loading && deliveryPersons.length === 0) {
    return <div className="flex justify-center items-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>;
  }
  return <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Motoqueiros</h2>
        <Button onClick={() => handleOpenDialog()} className="gap-2">
          <Plus className="h-4 w-4" />
          Adicionar
        </Button>
      </div>

      {deliveryPersons.length === 0 ? <div className="text-center py-8 bg-muted/20 rounded-lg">
          <p className="text-muted-foreground">Nenhum motoqueiro cadastrado</p>
          <Button onClick={() => handleOpenDialog()} variant="link" className="mt-2">
            Adicionar motoqueiro
          </Button>
        </div> : <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead className="w-[100px] text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {deliveryPersons.map(person => <TableRow key={person.id}>
                <TableCell className="font-medium">{person.name}</TableCell>
                <TableCell>{person.phone || "-"}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleOpenDialog(person)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive hover:text-destructive/80" onClick={() => handleDelete(person.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>)}
          </TableBody>
        </Table>}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentDeliveryPerson ? "Editar Motoqueiro" : "Novo Motoqueiro"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Nome
              </label>
              <Input id="name" value={name} onChange={e => setName(e.target.value)} placeholder="Nome do motoqueiro" />
            </div>
            <div className="space-y-2">
              <label htmlFor="phone" className="text-sm font-medium">
                Telefone
              </label>
              <div className="flex items-center">
                <span className="mr-2 text-sm text-muted-foreground">+244</span>
                <Input id="phone" value={phone} onChange={e => setPhone(e.target.value)} placeholder="Digite o número de telefone" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDialog}>
              Cancelar
            </Button>
            <Button onClick={handleSave} className="my-0">Salvar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>;
};
export default DeliveryPersonsSettings;