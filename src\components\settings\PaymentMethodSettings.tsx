import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchPaymentMethods, addPaymentMethod, updatePaymentMethod, deletePaymentMethod, PaymentMethod } from "@/services/paymentMethodService";
const PaymentMethodSettings = () => {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [newMethod, setNewMethod] = useState({
    name: ""
  });
  const {
    data: paymentMethods = [],
    isLoading
  } = useQuery({
    queryKey: ['paymentMethods'],
    queryFn: fetchPaymentMethods
  });
  const addMethodMutation = useMutation({
    mutationFn: addPaymentMethod,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['paymentMethods']
      });
      setNewMethod({
        name: ""
      });
      setIsAddDialogOpen(false);
    }
  });
  const updateMethodMutation = useMutation({
    mutationFn: updatePaymentMethod,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['paymentMethods']
      });
      setIsEditDialogOpen(false);
    }
  });
  const deleteMethodMutation = useMutation({
    mutationFn: deletePaymentMethod,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['paymentMethods']
      });
      setIsDeleteDialogOpen(false);
    }
  });
  const handleAddMethod = () => {
    addMethodMutation.mutate(newMethod);
  };
  const handleEditMethod = () => {
    if (selectedMethod) {
      updateMethodMutation.mutate(selectedMethod);
    }
  };
  const handleDeleteMethod = () => {
    if (selectedMethod) {
      deleteMethodMutation.mutate(selectedMethod.id);
    }
  };
  return <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Formas de Pagamento</h2>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Adicionar
        </Button>
      </div>
      
      {isLoading ? <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
        </div> : <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paymentMethods.map(method => <TableRow key={method.id}>
                <TableCell className="font-medium">{method.name}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" onClick={() => {
              setSelectedMethod(method);
              setIsEditDialogOpen(true);
            }}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700" onClick={() => {
              setSelectedMethod(method);
              setIsDeleteDialogOpen(true);
            }}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>)}
            
            {paymentMethods.length === 0 && <TableRow>
                <TableCell colSpan={2} className="text-center py-4 text-gray-500">
                  Nenhuma forma de pagamento cadastrada
                </TableCell>
              </TableRow>}
          </TableBody>
        </Table>}
      
      {/* Add Payment Method Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Forma de Pagamento</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="methodName">Nome da Forma de Pagamento</label>
              <Input id="methodName" value={newMethod.name} onChange={e => setNewMethod({
              name: e.target.value
            })} placeholder="Ex: Cartão de Crédito" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddMethod} disabled={addMethodMutation.isPending || !newMethod.name.trim()} className="my-0">
              {addMethodMutation.isPending ? "Adicionando..." : "Adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Payment Method Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Forma de Pagamento</DialogTitle>
          </DialogHeader>
          {selectedMethod && <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="editMethodName">Nome da Forma de Pagamento</label>
                <Input id="editMethodName" value={selectedMethod.name} onChange={e => setSelectedMethod({
              ...selectedMethod,
              name: e.target.value
            })} />
              </div>
            </div>}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleEditMethod} disabled={updateMethodMutation.isPending || !selectedMethod?.name.trim()} className="my-0">
              {updateMethodMutation.isPending ? "Salvando..." : "Salvar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
          </DialogHeader>
          <p>
            Tem certeza que deseja excluir a forma de pagamento "{selectedMethod?.name}"?
            Esta ação não pode ser desfeita.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancelar</Button>
            <Button variant="destructive" onClick={handleDeleteMethod} disabled={deleteMethodMutation.isPending}>
              {deleteMethodMutation.isPending ? "Excluindo..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>;
};
export default PaymentMethodSettings;