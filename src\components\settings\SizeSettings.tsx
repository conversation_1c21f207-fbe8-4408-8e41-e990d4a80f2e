import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchSizes, addSize, updateSize, deleteSize, Size } from "@/services/sizeService";
const SizeSettings = () => {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedSize, setSelectedSize] = useState<Size | null>(null);
  const [newSize, setNewSize] = useState({
    name: ""
  });
  const {
    data: sizes = [],
    isLoading
  } = useQuery({
    queryKey: ['sizes'],
    queryFn: fetchSizes
  });
  const addSizeMutation = useMutation({
    mutationFn: addSize,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['sizes']
      });
      setNewSize({
        name: ""
      });
      setIsAddDialogOpen(false);
    }
  });
  const updateSizeMutation = useMutation({
    mutationFn: updateSize,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['sizes']
      });
      setIsEditDialogOpen(false);
    }
  });
  const deleteSizeMutation = useMutation({
    mutationFn: deleteSize,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['sizes']
      });
      setIsDeleteDialogOpen(false);
    }
  });
  const handleAddSize = () => {
    addSizeMutation.mutate(newSize);
  };
  const handleEditSize = () => {
    if (selectedSize) {
      updateSizeMutation.mutate(selectedSize);
    }
  };
  const handleDeleteSize = () => {
    if (selectedSize) {
      deleteSizeMutation.mutate(selectedSize.id);
    }
  };
  return <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Tamanhos</h2>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Adicionar
        </Button>
      </div>
      
      {isLoading ? <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
        </div> : <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sizes.map(size => <TableRow key={size.id}>
                <TableCell className="font-medium">{size.name}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" onClick={() => {
              setSelectedSize(size);
              setIsEditDialogOpen(true);
            }}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700" onClick={() => {
              setSelectedSize(size);
              setIsDeleteDialogOpen(true);
            }}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>)}
            
            {sizes.length === 0 && <TableRow>
                <TableCell colSpan={2} className="text-center py-4 text-gray-500">
                  Nenhum tamanho cadastrado
                </TableCell>
              </TableRow>}
          </TableBody>
        </Table>}
      
      {/* Add Size Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Tamanho</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="sizeName">Nome do Tamanho</label>
              <Input id="sizeName" value={newSize.name} onChange={e => setNewSize({
              name: e.target.value
            })} placeholder="Ex: M/L" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddSize} disabled={addSizeMutation.isPending || !newSize.name.trim()} className="my-0">
              {addSizeMutation.isPending ? "Adicionando..." : "Adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Size Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Tamanho</DialogTitle>
          </DialogHeader>
          {selectedSize && <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="editSizeName">Nome do Tamanho</label>
                <Input id="editSizeName" value={selectedSize.name} onChange={e => setSelectedSize({
              ...selectedSize,
              name: e.target.value
            })} />
              </div>
            </div>}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleEditSize} disabled={updateSizeMutation.isPending || !selectedSize?.name.trim()} className="my-0">
              {updateSizeMutation.isPending ? "Salvando..." : "Salvar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
          </DialogHeader>
          <p>
            Tem certeza que deseja excluir o tamanho "{selectedSize?.name}"?
            Esta ação não pode ser desfeita.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancelar</Button>
            <Button variant="destructive" onClick={handleDeleteSize} disabled={deleteSizeMutation.isPending}>
              {deleteSizeMutation.isPending ? "Excluindo..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>;
};
export default SizeSettings;