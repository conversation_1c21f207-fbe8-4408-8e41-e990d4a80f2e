import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchStatuses, addStatus, updateStatus, deleteStatus, Status } from "@/services/statusService";
const StatusSettings = () => {
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Status | null>(null);
  const [newStatus, setNewStatus] = useState({
    name: ""
  });
  const {
    data: statuses = [],
    isLoading
  } = useQuery({
    queryKey: ['statuses'],
    queryFn: fetchStatuses
  });
  const addStatusMutation = useMutation({
    mutationFn: addStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['statuses']
      });
      setNewStatus({
        name: ""
      });
      setIsAddDialogOpen(false);
    }
  });
  const updateStatusMutation = useMutation({
    mutationFn: updateStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['statuses']
      });
      setIsEditDialogOpen(false);
    }
  });
  const deleteStatusMutation = useMutation({
    mutationFn: deleteStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['statuses']
      });
      setIsDeleteDialogOpen(false);
    }
  });
  const handleAddStatus = () => {
    addStatusMutation.mutate(newStatus);
  };
  const handleEditStatus = () => {
    if (selectedStatus) {
      updateStatusMutation.mutate(selectedStatus);
    }
  };
  const handleDeleteStatus = () => {
    if (selectedStatus) {
      deleteStatusMutation.mutate(selectedStatus.id);
    }
  };
  return <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Status</h2>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Adicionar
        </Button>
      </div>
      
      {isLoading ? <div className="flex justify-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
        </div> : <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {statuses.map(status => <TableRow key={status.id}>
                <TableCell className="font-medium">{status.name}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" onClick={() => {
              setSelectedStatus(status);
              setIsEditDialogOpen(true);
            }}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700" onClick={() => {
              setSelectedStatus(status);
              setIsDeleteDialogOpen(true);
            }}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>)}
            
            {statuses.length === 0 && <TableRow>
                <TableCell colSpan={2} className="text-center py-4 text-gray-500">
                  Nenhum status cadastrado
                </TableCell>
              </TableRow>}
          </TableBody>
        </Table>}
      
      {/* Add Status Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Status</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="statusName">Nome do Status</label>
              <Input id="statusName" value={newStatus.name} onChange={e => setNewStatus({
              name: e.target.value
            })} placeholder="Ex: Em Processamento" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleAddStatus} disabled={addStatusMutation.isPending || !newStatus.name.trim()} className="my-0">
              {addStatusMutation.isPending ? "Adicionando..." : "Adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Status</DialogTitle>
          </DialogHeader>
          {selectedStatus && <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="editStatusName">Nome do Status</label>
                <Input id="editStatusName" value={selectedStatus.name} onChange={e => setSelectedStatus({
              ...selectedStatus,
              name: e.target.value
            })} />
              </div>
            </div>}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleEditStatus} disabled={updateStatusMutation.isPending || !selectedStatus?.name.trim()} className="my-0">
              {updateStatusMutation.isPending ? "Salvando..." : "Salvar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
          </DialogHeader>
          <p>
            Tem certeza que deseja excluir o status "{selectedStatus?.name}"?
            Esta ação não pode ser desfeita.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancelar</Button>
            <Button variant="destructive" onClick={handleDeleteStatus} disabled={deleteStatusMutation.isPending}>
              {deleteStatusMutation.isPending ? "Excluindo..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>;
};
export default StatusSettings;