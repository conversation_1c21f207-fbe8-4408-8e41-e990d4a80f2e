
import React from "react";
import { IMaskInput } from "react-imask";
import { cn } from "@/lib/utils";

// Define the props for our MaskedInput component using a more generic approach
// to avoid type incompatibilities with IMaskInput
export interface MaskedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'mask'> {
  mask: string | RegExp | any; // Use any to avoid complex IMask typing issues
  placeholder?: string;
  unmask?: boolean;
  disabled?: boolean; // Added disabled prop explicitly
}

const MaskedInput = React.forwardRef<HTMLInputElement, MaskedInputProps>(
  ({ className, mask, placeholder, unmask = false, disabled = false, ...props }, ref) => {
    // Create a ref callback to properly handle the forwarded ref
    const inputRefCallback = (el: HTMLInputElement | null) => {
      if (typeof ref === 'function') {
        ref(el);
      } else if (ref) {
        ref.current = el;
      }
    };

    // For this temporary fix, we'll use a regular input instead of IMaskInput
    // to ensure that the phone number can be entered without restrictions
    return (
      <input
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        placeholder={placeholder || ""}
        disabled={disabled}
        ref={inputRefCallback}
        {...props}
      />
    );
  }
);

MaskedInput.displayName = "MaskedInput";

export { MaskedInput };
