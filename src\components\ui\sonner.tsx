
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"
import { CheckCircle2, XCircle, AlertTriangle, Info } from "lucide-react"
import { cn } from "@/lib/utils"

type ToasterProps = React.ComponentProps<typeof Sonner>

/**
 * Componente Toaster personalizado que exibe notificações toast com estilos distintos
 * baseados no tipo de notificação (sucesso, erro, informação, aviso).
 * Inclui uma barra de progresso animada e ícones específicos para cada tipo.
 *
 * @component
 * @param {ToasterProps} props - Propriedades do componente Sonner
 * @returns {JSX.Element} Componente Toaster configurado com estilos personalizados
 */
const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="top-right"
      expand={false}
      closeButton={false}
      icons={{
        success: <CheckCircle2 className="text-green-500 h-5 w-5" />,
        error: <XCircle className="text-red-500 h-5 w-5" />,
        warning: <AlertTriangle className="text-yellow-500 h-5 w-5" />,
        info: <Info className="text-blue-500 h-5 w-5" />,
      }}
      toastOptions={{
        duration: 4000,
        classNames: {
          toast: cn(
            "group toast relative flex flex-col overflow-hidden rounded-md p-4 pr-8",
            "group-[.toaster]:bg-background group-[.toaster]:text-foreground",
            "group-[.toaster]:border group-[.toaster]:border-border group-[.toaster]:shadow-lg",
            "transition-all duration-300 ease-in-out"
          ),
          title: "font-semibold text-base",
          description: "text-sm text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          success: cn(
            "group-[.toaster]:border-l-4 group-[.toaster]:border-l-green-500",
            "before:absolute before:bottom-0 before:left-0 before:right-0 before:h-1 before:bg-green-500",
            "before:animate-progress"
          ),
          error: cn(
            "group-[.toaster]:border-l-4 group-[.toaster]:border-l-red-500",
            "before:absolute before:bottom-0 before:left-0 before:right-0 before:h-1 before:bg-red-500",
            "before:animate-progress"
          ),
          warning: cn(
            "group-[.toaster]:border-l-4 group-[.toaster]:border-l-yellow-500",
            "before:absolute before:bottom-0 before:left-0 before:right-0 before:h-1 before:bg-yellow-500",
            "before:animate-progress"
          ),
          info: cn(
            "group-[.toaster]:border-l-4 group-[.toaster]:border-l-blue-500",
            "before:absolute before:bottom-0 before:left-0 before:right-0 before:h-1 before:bg-blue-500",
            "before:animate-progress"
          ),
          default: cn(
            "group-[.toaster]:border-l-4 group-[.toaster]:border-l-blue-500",
            "before:absolute before:bottom-0 before:left-0 before:right-0 before:h-1 before:bg-blue-500",
            "before:animate-progress"
          ),
        },
      }}
      {...props}
    />
  )
}

export { Toaster }
