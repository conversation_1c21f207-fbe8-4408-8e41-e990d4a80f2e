/**
 * @file EmptyState.tsx
 * @description Componente que exibe um estado vazio para a tabela de usuários quando
 * nenhum usuário é encontrado. Fornece feedback visual e orientação para o usuário.
 */

import React from "react";
import { User } from "lucide-react";

/**
 * Componente de estado vazio para a tabela de usuários
 * Exibe uma mensagem amigável quando nenhum usuário é encontrado
 *
 * @component
 * @returns {JSX.Element} Componente de estado vazio renderizado
 */
const EmptyState: React.FC = () => {
  return (
    <div className="py-12 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
      <User className="h-12 w-12 mb-4 opacity-20" />
      <h3 className="text-lg font-medium mb-1">Nenhum usuário encontrado</h3>
      <p className="text-sm">Tente ajustar os filtros de busca</p>
    </div>
  );
};

export default EmptyState;
