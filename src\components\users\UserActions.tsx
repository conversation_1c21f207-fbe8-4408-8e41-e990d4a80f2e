
import React from "react";
import { Edit, Trash2, User<PERSON><PERSON><PERSON>, UserX } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { User } from "@/pages/Users";

interface UserActionsProps {
  user: User;
  onEdit: (user: User) => void;
  onToggleActive: (user: User) => void;
  onDelete: (user: User) => void;
}

const UserActions: React.FC<UserActionsProps> = ({ 
  user, 
  onEdit, 
  onToggleActive, 
  onDelete 
}) => {
  return (
    <div className="flex justify-end gap-2">
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0"
        onClick={() => onEdit(user)}
        title="Editar usuário"
      >
        <Edit className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 w-8 p-0", 
          user.active 
            ? "text-yellow-500 hover:text-yellow-700" 
            : "text-green-500 hover:text-green-700"
        )}
        onClick={() => onToggleActive(user)}
        title={user.active ? "Desativar usuário" : "Ativar usuário"}
      >
        {user.active ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
        onClick={() => onDelete(user)}
        title="Excluir usuário"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default UserActions;
