
import React from "react";
import { Mail } from "lucide-react";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { isGeneratedEmail } from "./UserRoleUtils";

interface UserEmailProps {
  email: string;
}

const UserEmail: React.FC<UserEmailProps> = ({ email }) => {
  if (isGeneratedEmail(email)) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1">
              <span className="italic text-gray-400">{email}</span>
              <Mail className="h-4 w-4 text-yellow-500" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Email mascarado. Verificação de permissões pendente.</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  return <span>{email}</span>;
};

export default UserEmail;
