
import { UserRole } from "@/pages/Users";

// Map user roles to display labels
export const getUserRoleLabel = (role: UserRole): string => {
  const roleLabels: Record<UserRole, string> = {
    admin: "Administrador",
    vendedor: "Vendedor"
  };
  return roleLabels[role] || role;
};

// Get styled badge for user roles
export const getUserRoleBadge = (role: UserRole) => {
  const styles: Record<UserRole, string> = {
    admin: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400",
    vendedor: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
  };
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[role]}`}>
      {getUserRoleLabel(role)}
    </span>
  );
};

// Check if email is generated/masked
export const isGeneratedEmail = (email: string): boolean => {
  return email.includes('@oluchys.com') && /[a-f0-9]{8}@oluchys\.com/.test(email);
};
