
import React from "react";
import { cn } from "@/lib/utils";
import { User } from "@/pages/Users";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import UserEmail from "./UserEmail";
import UserActions from "./UserActions";
import { getUserRoleBadge } from "./UserRoleUtils";

interface UserRowProps {
  user: User;
  onEdit: (user: User) => void;
  onToggleActive: (user: User) => void;
  onDelete: (user: User) => void;
}

const UserRow: React.FC<UserRowProps> = ({ 
  user, 
  onEdit, 
  onToggleActive, 
  onDelete 
}) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <tr 
      className={cn(
        "hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",
        !user.active && "bg-gray-50 dark:bg-gray-800/50 opacity-60"
      )}
    >
      <td className="px-6 py-4 font-medium">{user.name}</td>
      <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
        <UserEmail email={user.email} />
      </td>
      <td className="px-6 py-4 text-gray-500 dark:text-gray-400">{user.phone || '-'}</td>
      <td className="px-6 py-4">{getUserRoleBadge(user.role)}</td>
      <td className="px-6 py-4">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          user.active 
            ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" 
            : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
        }`}>
          {user.active ? "Ativo" : "Inativo"}
        </span>
      </td>
      <td className="px-6 py-4 text-gray-500 dark:text-gray-400">{formatDate(user.created_at)}</td>
      <td className="px-6 py-4 text-right">
        <UserActions 
          user={user} 
          onEdit={onEdit} 
          onToggleActive={onToggleActive} 
          onDelete={onDelete} 
        />
      </td>
    </tr>
  );
};

export default UserRow;
