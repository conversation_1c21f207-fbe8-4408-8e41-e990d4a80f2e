/**
 * @file AuthContext.tsx
 * @description Contexto de autenticação para gerenciar o estado de login do usuário,
 * sessão, perfil e fornecer métodos para login, registro e logout.
 * Utiliza Supabase para autenticação e gerenciamento de usuários.
 */

import { createContext, useContext, useState, useEffect } from 'react';
import {
  Session,
  User,
} from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "sonner";
import { Profile } from '@/types';

/**
 * Interface que define os valores e métodos disponíveis no contexto de autenticação
 */
interface AuthContextType {
  /** Sessão atual do usuário no Supabase */
  session: Session | null;
  /** Objeto de usuário do Supabase */
  user: User | null;
  /** Perfil do usuário com informações adicionais do banco de dados */
  profile: Profile | null;
  /** Função para realizar login com email e senha */
  signIn: (email: string, password: string) => Promise<boolean>;
  /** Função para registrar um novo usuário */
  signUp: (email: string, password: string) => Promise<boolean>;
  /** Função para realizar logout */
  signOut: () => Promise<void>;
  /** Indica se o contexto está carregando dados iniciais */
  isLoading: boolean;
}

/** Contexto de autenticação */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Hook personalizado para acessar o contexto de autenticação
 *
 * @example
 * // Em um componente
 * const { user, signIn, signOut } = useAuth();
 *
 * // Verificar se o usuário está logado
 * if (user) {
 *   // Usuário está autenticado
 * }
 *
 * // Realizar login
 * const handleLogin = async () => {
 *   const success = await signIn(email, password);
 * };
 *
 * @returns {AuthContextType} Valores e métodos do contexto de autenticação
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

/** Props para o componente AuthProvider */
interface AuthProviderProps {
  /** Componentes filhos que terão acesso ao contexto */
  children: React.ReactNode;
}

/**
 * Verifica se a conta do usuário está ativa no sistema
 *
 * @param {string} userId - ID do usuário a ser verificado
 * @returns {Promise<boolean>} true se a conta estiver ativa, false caso contrário
 */
const checkUserActive = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('active')
    .eq('id', userId)
    .single();

  if (error) {
    console.error("Erro ao verificar status do usuário:", error);
    return true; // Em caso de erro, permite o login por padrão
  }

  return data?.active || false;
};

/**
 * Busca o perfil completo do usuário no banco de dados
 *
 * @param {string} userId - ID do usuário
 * @returns {Promise<Profile | null>} Perfil do usuário ou null em caso de erro
 */
const fetchUserProfile = async (userId: string): Promise<Profile | null> => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error("Erro ao buscar perfil do usuário:", error);
    return null;
  }

  return data as Profile;
};

/**
 * Provedor de contexto de autenticação que gerencia o estado de login do usuário
 * e fornece métodos para autenticação
 *
 * @component
 * @param {AuthProviderProps} props - Propriedades do componente
 * @returns {JSX.Element} Provedor de contexto de autenticação
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Estados para armazenar informações do usuário
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Controla o estado de carregamento inicial

  /**
   * Efeito para inicializar o estado de autenticação e configurar listeners
   * para mudanças no estado de autenticação
   */
  useEffect(() => {
    // Configura listener para mudanças no estado de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setSession(session);
      setUser(session?.user ?? null);

      // Busca o perfil do usuário quando a sessão muda
      if (session?.user) {
        setTimeout(() => {
          fetchUserProfile(session.user.id).then(profile => {
            setProfile(profile);
          });
        }, 0);
      } else {
        setProfile(null);
      }
    });

    /**
     * Busca a sessão atual do usuário ao inicializar o contexto
     */
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        const profile = await fetchUserProfile(session.user.id);
        setProfile(profile);
      }

      setIsLoading(false); // Finaliza o carregamento inicial
    };

    getSession();

    // Limpa o listener quando o componente é desmontado
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  /**
   * Realiza o login do usuário com email e senha
   * Verifica também se a conta está ativa antes de permitir o acesso
   *
   * @param {string} email - Email do usuário
   * @param {string} password - Senha do usuário
   * @returns {Promise<boolean>} true se o login for bem-sucedido, false caso contrário
   */
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Verifica se a conta do usuário está ativa
      const isActive = await checkUserActive(data.user.id);

      if (!isActive) {
        await supabase.auth.signOut();
        toast.error("Acesso bloqueado", {
          description: "Sua conta está desativada. Entre em contato com o administrador."
        });
        return false;
      }

      toast.success("Login realizado com sucesso", {
        description: "Bem-vindo de volta!"
      });

      return true;
    } catch (error: any) {
      toast.error("Erro ao fazer login", {
        description: error.message
      });
      return false;
    }
  };

  /**
   * Registra um novo usuário com email e senha
   *
   * @param {string} email - Email do novo usuário
   * @param {string} password - Senha do novo usuário
   * @returns {Promise<boolean>} true se o registro for bem-sucedido, false caso contrário
   */
  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) throw error;

      toast.success("Conta criada com sucesso", {
        description: "Verifique seu email para confirmar o cadastro."
      });

      return true;
    } catch (error: any) {
      toast.error("Erro ao criar conta", {
        description: error.message
      });
      return false;
    }
  };

  /**
   * Realiza o logout do usuário atual
   */
  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      toast.success("Logout realizado com sucesso", {
        description: "Você foi desconectado."
      });
    } catch (error: any) {
      toast.error("Erro ao fazer logout", {
        description: error.message
      });
    }
  };

  const value: AuthContextType = {
    session,
    user,
    profile,
    signIn,
    signUp,
    signOut,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
