/**
 * @file SidebarContext.tsx
 * @description Contexto para gerenciar o estado da barra lateral (expandida/recolhida)
 * e persistir a preferência do usuário entre sessões usando localStorage.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';

/**
 * Interface que define os valores e métodos disponíveis no contexto da barra lateral
 */
interface SidebarContextType {
  /** Estado atual da barra lateral (true = recolhida, false = expandida) */
  isCollapsed: boolean;
  /** Função para alternar entre os estados expandido e recolhido */
  toggleCollapsed: () => void;
}

/** Contexto da barra lateral */
const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

/**
 * Hook personalizado para acessar o contexto da barra lateral
 *
 * @example
 * // Em um componente
 * const { isCollapsed, toggleCollapsed } = useSidebar();
 *
 * // Verificar se a barra lateral está recolhida
 * if (isCollapsed) {
 *   // Renderizar versão compacta
 * }
 *
 * // Botão para alternar o estado
 * <button onClick={toggleCollapsed}>Toggle Sidebar</button>
 *
 * @returns {SidebarContextType} Valores e métodos do contexto da barra lateral
 */
export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

/** Props para o componente SidebarProvider */
interface SidebarProviderProps {
  /** Componentes filhos que terão acesso ao contexto */
  children: React.ReactNode;
}

/**
 * Provedor de contexto da barra lateral que gerencia o estado
 * expandido/recolhido e persiste a preferência do usuário
 *
 * @component
 * @param {SidebarProviderProps} props - Propriedades do componente
 * @returns {JSX.Element} Provedor de contexto da barra lateral
 */
export const SidebarProvider: React.FC<SidebarProviderProps> = ({ children }) => {
  /**
   * Estado que controla se a barra lateral está recolhida ou expandida
   * Inicializado com o valor salvo no localStorage ou expandido por padrão
   */
  const [isCollapsed, setIsCollapsed] = useState(() => {
    // Verifica o localStorage para preferência salva
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  /**
   * Efeito para salvar a preferência do usuário no localStorage
   * quando o estado da barra lateral muda
   */
  useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  /**
   * Efeito para sincronizar o estado entre diferentes abas/janelas
   * Escuta mudanças no localStorage feitas por outras instâncias da aplicação
   */
  useEffect(() => {
    const handleStorageChange = () => {
      const saved = localStorage.getItem('sidebar-collapsed');
      if (saved) {
        setIsCollapsed(JSON.parse(saved));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  /**
   * Função para alternar o estado da barra lateral entre expandido e recolhido
   */
  const toggleCollapsed = () => {
    setIsCollapsed(prev => !prev);
  };

  return (
    <SidebarContext.Provider value={{ isCollapsed, toggleCollapsed }}>
      {children}
    </SidebarContext.Provider>
  );
};
