
import { useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Notification } from "@/types";
import { useAuth } from "@/context/AuthContext";

export const useNotifications = () => {
  const queryClient = useQueryClient();
  const { user, profile } = useAuth();
  
  // Buscar notificações com filtro baseado no perfil do usuário
  const fetchNotifications = async (): Promise<Notification[]> => {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error('Erro ao buscar notificações:', error);
      throw new Error(error.message);
    }
    
    // Filtrar notificações baseado no perfil do usuário
    // Administradores veem todos os tipos de notificações
    // Vendedores veem apenas notificações de estoque
    const filteredNotifications = data.filter(notification => {
      if (profile?.role === 'admin') return true; // Administradores veem todas as notificações
      if (profile?.role === 'vendedor' && notification.type === 'estoque') return true;
      return false;
    });
    
    return filteredNotifications;
  };
  
  // Manter as funções existentes de marcar como lida

  const markAsRead = async (id: string) => {
    const { data, error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', id)
      .select();
      
    if (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      throw new Error(error.message);
    }
    
    return data[0];
  };
  
  const markAllAsRead = async () => {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('read', false);
      
    if (error) {
      console.error('Erro ao marcar todas notificações como lidas:', error);
      throw new Error(error.message);
    }
    
    return true;
  };
  
  // Use React Query para gerenciar estado e cache
  const notificationsQuery = useQuery({
    queryKey: ['notifications'],
    queryFn: fetchNotifications,
  });
  
  // Mutations
  const markAsReadMutation = useMutation({
    mutationFn: markAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
  
  const markAllAsReadMutation = useMutation({
    mutationFn: markAllAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
  
  // Escutar por mudanças em tempo real
  useEffect(() => {
    const channel = supabase
      .channel('notifications-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'notifications' 
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);
  
  const unreadCount = notificationsQuery.data?.filter(n => !n.read).length || 0;
  
  return {
    notifications: notificationsQuery.data || [],
    isLoading: notificationsQuery.isLoading,
    isError: notificationsQuery.isError,
    error: notificationsQuery.error,
    unreadCount,
    markAsRead: markAsReadMutation.mutate,
    markAllAsRead: markAllAsReadMutation.mutate,
  };
};
