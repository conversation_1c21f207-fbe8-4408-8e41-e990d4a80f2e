import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { pt } from 'date-fns/locale';

export type FilterPeriod = 'today' | 'thisWeek' | 'thisMonth' | 'custom';

interface ReportFilters {
  period: FilterPeriod;
  startDate: string | null;
  endDate: string | null;
  sellerId: string | null;
  deliveryPersonId: string | null;
  paymentMethodId: string | null;
  statusId: string | null;
}

export function useReports() {
  const today = new Date();
  const [filters, setFilters] = useState<ReportFilters>({
    period: 'thisWeek',
    startDate: format(startOfWeek(today, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
    endDate: format(endOfWeek(today, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
    sellerId: null,
    deliveryPersonId: null,
    paymentMethodId: null,
    statusId: null,
  });

  const updateFilters = (newFilters: Partial<ReportFilters>) => {
    if (newFilters.period && newFilters.period !== filters.period) {
      const today = new Date();
      
      switch (newFilters.period) {
        case 'today':
          newFilters.startDate = format(today, 'yyyy-MM-dd');
          newFilters.endDate = format(today, 'yyyy-MM-dd');
          break;
        case 'thisWeek':
          newFilters.startDate = format(startOfWeek(today, { weekStartsOn: 1 }), 'yyyy-MM-dd');
          newFilters.endDate = format(endOfWeek(today, { weekStartsOn: 1 }), 'yyyy-MM-dd');
          break;
        case 'thisMonth':
          newFilters.startDate = format(startOfMonth(today), 'yyyy-MM-dd');
          newFilters.endDate = format(endOfMonth(today), 'yyyy-MM-dd');
          break;
        case 'custom':
          if (!newFilters.startDate) newFilters.startDate = filters.startDate;
          if (!newFilters.endDate) newFilters.endDate = filters.endDate;
          break;
      }
    }
    
    if (newFilters.sellerId === 'all') newFilters.sellerId = null;
    if (newFilters.statusId === 'all') newFilters.statusId = null;
    if (newFilters.paymentMethodId === 'all') newFilters.paymentMethodId = null;
    if (newFilters.deliveryPersonId === 'all') newFilters.deliveryPersonId = null;

    setFilters({ ...filters, ...newFilters });
  };

  const fetchSalesBySeller = async () => {
    try {
      let query = supabase
        .from('sales')
        .select(`
          id,
          price,
          sale_date,
          status:sale_statuses(id, name),
          seller:profiles!sales_seller_id_fkey(id, name)
        `)
        .order('sale_date', { ascending: false });

      if (filters.startDate) {
        query = query.gte('sale_date', `${filters.startDate}T00:00:00.000Z`);
      }
      if (filters.endDate) {
        query = query.lte('sale_date', `${filters.endDate}T23:59:59.999Z`);
      }
      if (filters.sellerId) {
        query = query.eq('seller_id', filters.sellerId);
      }
      if (filters.statusId) {
        query = query.eq('status_id', filters.statusId);
      }
      if (filters.paymentMethodId) {
        query = query.eq('payment_method_id', filters.paymentMethodId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const sellerMap = new Map();
      
      data.forEach(sale => {
        const sellerId = sale.seller?.id;
        const sellerName = sale.seller?.name || 'Desconhecido';
        
        if (!sellerId) return;

        if (!sellerMap.has(sellerId)) {
          sellerMap.set(sellerId, {
            id: sellerId,
            name: sellerName,
            totalSales: 0,
            totalAmount: 0,
            sales: []
          });
        }

        sellerMap.get(sellerId).totalSales += 1;
        sellerMap.get(sellerId).totalAmount += Number(sale.price) || 0;
        sellerMap.get(sellerId).sales.push(sale);
      });

      return Array.from(sellerMap.values()).sort((a, b) => b.totalAmount - a.totalAmount);
    } catch (error) {
      console.error('Erro ao buscar vendas por vendedor:', error);
      throw error;
    }
  };

  const fetchSalesByDelivery = async () => {
    try {
      let query = supabase
        .from('sales')
        .select(`
          id,
          price,
          sale_date,
          status:sale_statuses(id, name),
          delivery_person:delivery_persons(id, name)
        `)
        .not('delivery_person_id', 'is', null)
        .order('sale_date', { ascending: false });

      if (filters.startDate) {
        query = query.gte('sale_date', `${filters.startDate}T00:00:00.000Z`);
      }
      if (filters.endDate) {
        query = query.lte('sale_date', `${filters.endDate}T23:59:59.999Z`);
      }
      if (filters.deliveryPersonId) {
        query = query.eq('delivery_person_id', filters.deliveryPersonId);
      }
      if (filters.statusId) {
        query = query.eq('status_id', filters.statusId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const deliveryMap = new Map();
      
      data.forEach(sale => {
        const deliveryId = sale.delivery_person?.id;
        const deliveryName = sale.delivery_person?.name || 'Desconhecido';
        
        if (!deliveryId) return;

        if (!deliveryMap.has(deliveryId)) {
          deliveryMap.set(deliveryId, {
            id: deliveryId,
            name: deliveryName,
            totalDeliveries: 0,
            totalAmount: 0,
            deliveries: []
          });
        }

        deliveryMap.get(deliveryId).totalDeliveries += 1;
        deliveryMap.get(deliveryId).totalAmount += Number(sale.price) || 0;
        deliveryMap.get(deliveryId).deliveries.push(sale);
      });

      return Array.from(deliveryMap.values()).sort((a, b) => b.totalDeliveries - a.totalDeliveries);
    } catch (error) {
      console.error('Erro ao buscar vendas por entregador:', error);
      throw error;
    }
  };

  const fetchSalesByPayment = async () => {
    try {
      let query = supabase
        .from('sales')
        .select(`
          id,
          price,
          sale_date,
          status:sale_statuses(id, name),
          payment_method:payment_methods(id, name)
        `)
        .order('sale_date', { ascending: false });

      if (filters.startDate) {
        query = query.gte('sale_date', `${filters.startDate}T00:00:00.000Z`);
      }
      if (filters.endDate) {
        query = query.lte('sale_date', `${filters.endDate}T23:59:59.999Z`);
      }
      if (filters.paymentMethodId) {
        query = query.eq('payment_method_id', filters.paymentMethodId);
      }
      if (filters.statusId) {
        query = query.eq('status_id', filters.statusId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const paymentMap = new Map();
      
      data.forEach(sale => {
        const paymentId = sale.payment_method?.id;
        const paymentName = sale.payment_method?.name || 'Desconhecido';
        
        if (!paymentId) return;

        if (!paymentMap.has(paymentId)) {
          paymentMap.set(paymentId, {
            id: paymentId,
            name: paymentName,
            totalSales: 0,
            totalAmount: 0,
            sales: []
          });
        }

        paymentMap.get(paymentId).totalSales += 1;
        paymentMap.get(paymentId).totalAmount += Number(sale.price) || 0;
        paymentMap.get(paymentId).sales.push(sale);
      });

      return Array.from(paymentMap.values()).sort((a, b) => b.totalAmount - a.totalAmount);
    } catch (error) {
      console.error('Erro ao buscar vendas por forma de pagamento:', error);
      throw error;
    }
  };

  const fetchTopProducts = async () => {
    try {
      let query = supabase
        .from('sales')
        .select(`
          id,
          price,
          sale_date,
          status:sale_statuses(id, name),
          product_variant:product_variants(
            id,
            product:products(id, name),
            color:colors(id, name),
            size:sizes(id, name)
          )
        `)
        .order('sale_date', { ascending: false });

      if (filters.startDate) {
        query = query.gte('sale_date', `${filters.startDate}T00:00:00.000Z`);
      }
      if (filters.endDate) {
        query = query.lte('sale_date', `${filters.endDate}T23:59:59.999Z`);
      }
      if (filters.statusId) {
        query = query.eq('status_id', filters.statusId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const productMap = new Map();
      
      data.forEach(sale => {
        const productId = sale.product_variant?.product?.id;
        const productName = sale.product_variant?.product?.name || 'Desconhecido';
        
        if (!productId) return;

        if (!productMap.has(productId)) {
          productMap.set(productId, {
            id: productId,
            name: productName,
            totalSales: 0,
            totalAmount: 0,
            variants: new Set(),
            sales: []
          });
        }

        productMap.get(productId).totalSales += 1;
        productMap.get(productId).totalAmount += Number(sale.price) || 0;
        productMap.get(productId).variants.add(JSON.stringify({
          color: sale.product_variant?.color?.name,
          size: sale.product_variant?.size?.name
        }));
        productMap.get(productId).sales.push(sale);
      });

      const result = Array.from(productMap.values()).map(product => ({
        ...product,
        variants: Array.from(product.variants).map(v => {
          if (typeof v === 'string') {
            return JSON.parse(v);
          }
          return { color: 'Desconhecido', size: 'Desconhecido' };
        }),
        variantCount: product.variants.size
      }));

      return result.sort((a, b) => b.totalSales - a.totalSales);
    } catch (error) {
      console.error('Erro ao buscar produtos mais vendidos:', error);
      throw error;
    }
  };

  const fetchPeriodSummary = async () => {
    try {
      let query = supabase
        .from('sales')
        .select(`
          id,
          price,
          sale_date,
          status:sale_statuses(id, name)
        `)
        .order('sale_date', { ascending: false });

      if (filters.startDate) {
        query = query.gte('sale_date', `${filters.startDate}T00:00:00.000Z`);
      }
      if (filters.endDate) {
        query = query.lte('sale_date', `${filters.endDate}T23:59:59.999Z`);
      }
      if (filters.statusId) {
        query = query.eq('status_id', filters.statusId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const dailyMap = new Map();
      let totalSales = 0;
      let totalAmount = 0;
      let realizadas = 0;
      let reservadas = 0;
      let canceladas = 0;
      
      data.forEach(sale => {
        const saleDate = sale.sale_date ? new Date(sale.sale_date) : new Date();
        const dateKey = format(saleDate, 'yyyy-MM-dd');
        const statusName = sale.status?.name || '';
        
        if (statusName.toLowerCase() === 'realizado') realizadas++;
        else if (statusName.toLowerCase() === 'reservado') reservadas++;
        else if (statusName.toLowerCase() === 'cancelado') canceladas++;

        if (!dailyMap.has(dateKey)) {
          dailyMap.set(dateKey, {
            date: dateKey,
            formattedDate: format(saleDate, 'dd/MM/yyyy'),
            weekday: format(saleDate, 'EEEE', { locale: pt }),
            sales: 0,
            amount: 0
          });
        }

        if (statusName.toLowerCase() === 'realizado') {
          dailyMap.get(dateKey).sales += 1;
          dailyMap.get(dateKey).amount += Number(sale.price) || 0;
          totalSales += 1;
          totalAmount += Number(sale.price) || 0;
        }
      });

      const dailySales = Array.from(dailyMap.values()).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      return {
        totalSales,
        totalAmount,
        realizadas,
        reservadas,
        canceladas,
        dailySales
      };
    } catch (error) {
      console.error('Erro ao buscar resumo do período:', error);
      throw error;
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const { data: sellersData, error: sellersError } = await supabase
        .from('profiles')
        .select('id, name')
        .in('role', ['admin', 'vendedor'])
        .eq('active', true)
        .order('name');

      if (sellersError) throw sellersError;

      const { data: deliveryData, error: deliveryError } = await supabase
        .from('delivery_persons')
        .select('id, name')
        .order('name');

      if (deliveryError) throw deliveryError;

      const { data: paymentData, error: paymentError } = await supabase
        .from('payment_methods')
        .select('id, name')
        .order('name');

      if (paymentError) throw paymentError;

      const { data: statusData, error: statusError } = await supabase
        .from('sale_statuses')
        .select('id, name')
        .order('name');

      if (statusError) throw statusError;

      return {
        sellers: sellersData,
        deliveryPersons: deliveryData,
        paymentMethods: paymentData,
        statuses: statusData
      };
    } catch (error) {
      console.error('Erro ao buscar opções de filtro:', error);
      throw error;
    }
  };

  const sellerReportQuery = useQuery({
    queryKey: ['sellerReport', filters],
    queryFn: fetchSalesBySeller,
  });

  const deliveryReportQuery = useQuery({
    queryKey: ['deliveryReport', filters],
    queryFn: fetchSalesByDelivery,
  });

  const paymentReportQuery = useQuery({
    queryKey: ['paymentReport', filters],
    queryFn: fetchSalesByPayment,
  });

  const topProductsQuery = useQuery({
    queryKey: ['topProducts', filters],
    queryFn: fetchTopProducts,
  });

  const periodSummaryQuery = useQuery({
    queryKey: ['periodSummary', filters],
    queryFn: fetchPeriodSummary,
  });

  const filterOptionsQuery = useQuery({
    queryKey: ['filterOptions'],
    queryFn: fetchFilterOptions,
  });

  const formattedDateRange = () => {
    if (!filters.startDate || !filters.endDate) return '';
    
    const startDate = new Date(filters.startDate);
    const endDate = new Date(filters.endDate);
    
    return `${format(startDate, 'dd/MM/yyyy')} a ${format(endDate, 'dd/MM/yyyy')}`;
  };

  return {
    filters,
    updateFilters,
    formattedDateRange,
    
    sellerReport: sellerReportQuery.data || [],
    deliveryReport: deliveryReportQuery.data || [],
    paymentReport: paymentReportQuery.data || [],
    topProducts: topProductsQuery.data || [],
    periodSummary: periodSummaryQuery.data || { 
      totalSales: 0, 
      totalAmount: 0,
      realizadas: 0,
      reservadas: 0,
      canceladas: 0,
      dailySales: []
    },
    
    filterOptions: filterOptionsQuery.data || {
      sellers: [],
      deliveryPersons: [],
      paymentMethods: [],
      statuses: []
    },
    
    isLoading: sellerReportQuery.isLoading || 
               deliveryReportQuery.isLoading || 
               paymentReportQuery.isLoading || 
               topProductsQuery.isLoading || 
               periodSummaryQuery.isLoading ||
               filterOptionsQuery.isLoading,
    
    isError: sellerReportQuery.isError || 
             deliveryReportQuery.isError || 
             paymentReportQuery.isError || 
             topProductsQuery.isError || 
             periodSummaryQuery.isError ||
             filterOptionsQuery.isError,
  };
}
