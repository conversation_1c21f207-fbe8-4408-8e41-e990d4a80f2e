
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Sale, SaleStatus, ProductVariant, PaymentMethod, Profile, DeliveryPerson } from "@/types";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

export const useSales = (filters?: {
  startDate?: string;
  endDate?: string;
  status?: string;
  seller?: string;
}) => {
  const queryClient = useQueryClient();
  
  // Função para buscar vendas
  const fetchSales = async (): Promise<Sale[]> => {
    let query = supabase
      .from('sales')
      .select(`
        *,
        product_variant:product_variants(
          id,
          product:products(id, name, category_id, price),
          color:colors(id, name, hex_code),
          size:sizes(id, name)
        ),
        payment_method:payment_methods(id, name),
        status:sale_statuses(id, name),
        seller:profiles!sales_seller_id_fkey(id, name, phone),
        delivery_person:delivery_persons(id, name, phone)
      `)
      .order('sale_date', { ascending: false });
    
    // Aplicar filtros se fornecidos
    if (filters?.startDate) {
      query = query.gte('sale_date', filters.startDate);
    }
    
    if (filters?.endDate) {
      query = query.lte('sale_date', filters.endDate);
    }
    
    if (filters?.status) {
      query = query.eq('status_id', filters.status);
    }
    
    if (filters?.seller) {
      query = query.eq('seller_id', filters.seller);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Erro ao buscar vendas:', error);
      throw new Error(error.message);
    }
    
    return data as Sale[];
  };
  
  // Função para buscar status de vendas
  const fetchSaleStatuses = async (): Promise<SaleStatus[]> => {
    const { data, error } = await supabase
      .from('sale_statuses')
      .select('*')
      .order('name');
    
    if (error) {
      console.error('Erro ao buscar status de vendas:', error);
      throw new Error(error.message);
    }
    
    return data as SaleStatus[];
  };
  
  // Função para buscar produtos e variantes
  const fetchProductVariants = async (): Promise<ProductVariant[]> => {
    const { data, error } = await supabase
      .from('product_variants')
      .select(`
        *,
        product:products(id, name, price),
        color:colors(id, name),
        size:sizes(id, name)
      `)
      .gt('quantity', 0); // Apenas produtos com estoque
    
    if (error) {
      console.error('Erro ao buscar variantes de produtos:', error);
      throw new Error(error.message);
    }
    
    return data as ProductVariant[];
  };
  
  // Função para buscar métodos de pagamento
  const fetchPaymentMethods = async (): Promise<PaymentMethod[]> => {
    const { data, error } = await supabase
      .from('payment_methods')
      .select('*')
      .order('name');
    
    if (error) {
      console.error('Erro ao buscar métodos de pagamento:', error);
      throw new Error(error.message);
    }
    
    return data as PaymentMethod[];
  };
  
  // Função para buscar vendedores
  const fetchSellers = async (): Promise<Profile[]> => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .in('role', ['admin', 'vendedor'])
      .eq('active', true)
      .order('name');
    
    if (error) {
      console.error('Erro ao buscar vendedores:', error);
      throw new Error(error.message);
    }
    
    return data as Profile[];
  };
  
  // Função para buscar motoqueiros
  const fetchDeliveryPersons = async (): Promise<DeliveryPerson[]> => {
    const { data, error } = await supabase
      .from('delivery_persons')
      .select('*')
      .order('name');
    
    if (error) {
      console.error('Erro ao buscar motoqueiros:', error);
      throw new Error(error.message);
    }
    
    return data as DeliveryPerson[];
  };
  
  // Função para calcular estatísticas
  const calculateStats = (sales: Sale[]) => {
    const realizadas = sales.filter(sale => 
      sale.status?.name === 'Realizado').length;
    
    const reservadas = sales.filter(sale => 
      sale.status?.name === 'Reservado').length;
    
    const canceladas = sales.filter(sale => 
      sale.status?.name === 'Cancelado').length;
    
    const totalValue = sales
      .filter(sale => sale.status?.name === 'Realizado')
      .reduce((sum, sale) => sum + Number(sale.price), 0);
    
    return { realizadas, reservadas, canceladas, totalValue };
  };
  
  // Upload de comprovativo
  const uploadPaymentProof = async (file: File): Promise<string> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `${fileName}`;
      
      const { error } = await supabase.storage
        .from('payment-proofs')
        .upload(filePath, file);
      
      if (error) {
        console.error('Erro ao fazer upload do comprovativo:', error);
        throw new Error(error.message);
      }
      
      const { data } = supabase.storage
        .from('payment-proofs')
        .getPublicUrl(filePath);
      
      return filePath;
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      throw error;
    }
  };
  
  // Adicionar venda
  const addSale = async ({ sale, proofFile }: { sale: Partial<Sale>, proofFile?: File }): Promise<Sale> => {
    try {
      // Upload do comprovativo se existir
      let paymentProofPath = null;
      if (proofFile) {
        paymentProofPath = await uploadPaymentProof(proofFile);
      }
      
      // Verificar campos obrigatórios
      if (!sale.customer_name || !sale.product_variant_id || 
          !sale.price || !sale.payment_method_id || 
          !sale.seller_id || !sale.status_id) {
        throw new Error('Campos obrigatórios faltando');
      }
      
      // Preparar os dados da venda
      const saleData = {
        customer_name: sale.customer_name,
        customer_contact: sale.customer_contact || null,
        product_variant_id: sale.product_variant_id,
        price: sale.price,
        payment_method_id: sale.payment_method_id,
        payment_proof: paymentProofPath,
        notes: sale.notes || null,
        seller_id: sale.seller_id,
        delivery_person_id: sale.delivery_person_id && sale.delivery_person_id !== "" 
          ? sale.delivery_person_id 
          : null,
        status_id: sale.status_id,
        sale_date: sale.sale_date || new Date().toISOString()
      };
      
      // Log dos dados para depuração
      console.log('Dados da venda a serem enviados:', saleData);
      
      const { data, error } = await supabase
        .from('sales')
        .insert(saleData)
        .select('*')
        .single();
      
      if (error) {
        console.error('Erro ao adicionar venda:', error);
        throw new Error(error.message);
      }
      
      return data as Sale;
    } catch (error) {
      console.error('Erro na função addSale:', error);
      throw error;
    }
  };
  
  // Atualizar venda
  const updateSale = async (
    id: string, 
    updates: Partial<Sale>, 
    proofFile?: File
  ): Promise<Sale> => {
    try {
      // Upload do comprovativo se existir
      if (proofFile) {
        updates.payment_proof = await uploadPaymentProof(proofFile);
      }
      
      // Garantir que delivery_person_id seja null se for string vazia
      if (updates.delivery_person_id === "") {
        updates.delivery_person_id = null;
      }
      
      const { data, error } = await supabase
        .from('sales')
        .update(updates)
        .eq('id', id)
        .select('*')
        .single();
      
      if (error) {
        console.error('Erro ao atualizar venda:', error);
        throw new Error(error.message);
      }
      
      return data as Sale;
    } catch (error) {
      console.error('Erro na função updateSale:', error);
      throw error;
    }
  };
  
  // Excluir venda
  const deleteSale = async (id: string): Promise<boolean> => {
    // Primeiro, buscar a venda para obter o comprovativo
    const { data: sale } = await supabase
      .from('sales')
      .select('payment_proof')
      .eq('id', id)
      .single();
    
    // Excluir a venda
    const { error } = await supabase
      .from('sales')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Erro ao excluir venda:', error);
      throw new Error(error.message);
    }
    
    // Se existir um comprovativo, excluí-lo também
    if (sale?.payment_proof) {
      const { error: storageError } = await supabase.storage
        .from('payment-proofs')
        .remove([sale.payment_proof]);
      
      if (storageError) {
        console.warn('Erro ao excluir comprovativo:', storageError);
        // Não falhar a operação por causa do comprovativo
      }
    }
    
    return true;
  };
  
  // Obter URL pública do comprovativo
  const getPaymentProofUrl = (path?: string): string | null => {
    if (!path) return null;
    
    const { data } = supabase.storage
      .from('payment-proofs')
      .getPublicUrl(path);
    
    return data.publicUrl;
  };
  
  // Use React Query para gerenciar estado e cache
  const salesQuery = useQuery({
    queryKey: ['sales', filters],
    queryFn: fetchSales,
  });
  
  const statusesQuery = useQuery({
    queryKey: ['saleStatuses'],
    queryFn: fetchSaleStatuses,
  });
  
  const productVariantsQuery = useQuery({
    queryKey: ['productVariants'],
    queryFn: fetchProductVariants,
  });
  
  const paymentMethodsQuery = useQuery({
    queryKey: ['paymentMethods'],
    queryFn: fetchPaymentMethods,
  });
  
  const sellersQuery = useQuery({
    queryKey: ['sellers'],
    queryFn: fetchSellers,
  });
  
  const deliveryPersonsQuery = useQuery({
    queryKey: ['deliveryPersons'],
    queryFn: fetchDeliveryPersons,
  });
  
  // Mutations
  const addMutation = useMutation({
    mutationFn: ({ sale, proofFile }: { sale: Partial<Sale>, proofFile?: File }) => 
      addSale({ sale, proofFile }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast("Venda registrada", {
        description: "A venda foi registrada com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao registrar venda", {
        description: error.message
      });
    },
  });
  
  const updateMutation = useMutation({
    mutationFn: ({ id, updates, proofFile }: { id: string; updates: Partial<Sale>; proofFile?: File }) => 
      updateSale(id, updates, proofFile),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast("Venda atualizada", {
        description: "A venda foi atualizada com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao atualizar venda", {
        description: error.message
      });
    },
  });
  
  const deleteMutation = useMutation({
    mutationFn: deleteSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast("Venda excluída", {
        description: "A venda foi excluída com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao excluir venda", {
        description: error.message
      });
    },
  });
  
  // Calcular estatísticas quando os dados estiverem disponíveis
  const stats = salesQuery.data ? calculateStats(salesQuery.data) : {
    realizadas: 0,
    reservadas: 0,
    canceladas: 0,
    totalValue: 0
  };
  
  return {
    sales: salesQuery.data || [],
    isLoading: salesQuery.isLoading || statusesQuery.isLoading || 
              productVariantsQuery.isLoading || paymentMethodsQuery.isLoading || 
              sellersQuery.isLoading || deliveryPersonsQuery.isLoading,
    isError: salesQuery.isError || statusesQuery.isError || 
            productVariantsQuery.isError || paymentMethodsQuery.isError || 
            sellersQuery.isError || deliveryPersonsQuery.isError,
    error: salesQuery.error || statusesQuery.error || 
          productVariantsQuery.error || paymentMethodsQuery.error || 
          sellersQuery.error || deliveryPersonsQuery.error,
    productVariants: productVariantsQuery.data || [],
    statuses: statusesQuery.data || [],
    paymentMethods: paymentMethodsQuery.data || [],
    sellers: sellersQuery.data || [],
    deliveryPersons: deliveryPersonsQuery.data || [],
    stats,
    addSale: addMutation.mutate,
    updateSale: updateMutation.mutate,
    deleteSale: deleteMutation.mutate,
    getPaymentProofUrl,
  };
};
