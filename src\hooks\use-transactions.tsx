import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Transaction, PaymentMethod } from "@/types";
import { toast } from "sonner";

export function useTransactions(filters?: {
  startDate?: string;
  endDate?: string;
  type?: 'entrada' | 'saida';
  paymentMethod?: string;
}) {
  const queryClient = useQueryClient();
  
  // Função para buscar transações
  const fetchTransactions = async (): Promise<Transaction[]> => {
    try {
      let query = supabase
        .from('transactions')
        .select(`
          *,
          payment_method:payment_methods(id, name)
        `)
        .order('transaction_date', { ascending: false });
      
      // Aplicar filtros se fornecidos e válidos
      if (filters?.startDate && /^\d{4}-\d{2}-\d{2}$/.test(filters.startDate)) {
        query = query.gte('transaction_date', filters.startDate);
      }
      
      if (filters?.endDate && /^\d{4}-\d{2}-\d{2}$/.test(filters.endDate)) {
        query = query.lte('transaction_date', filters.endDate);
      }
      
      if (filters?.type && (filters.type === 'entrada' || filters.type === 'saida')) {
        query = query.eq('type', filters.type);
      }
      
      if (filters?.paymentMethod && filters.paymentMethod.trim() !== '' && filters.paymentMethod !== '_all') {
        query = query.eq('payment_method_id', filters.paymentMethod);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Erro ao buscar transações:', error);
        throw new Error(error.message);
      }
      
      return data as Transaction[] || [];
    } catch (error) {
      console.error('Erro ao buscar transações:', error);
      throw error; // Re-throw to let React Query handle it
    }
  };
  
  // Função para buscar métodos de pagamento
  const fetchPaymentMethods = async (): Promise<PaymentMethod[]> => {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .select('*')
        .order('name');
      
      if (error) {
        console.error('Erro ao buscar métodos de pagamento:', error);
        throw new Error(error.message);
      }
      
      return data || [];
    } catch (error) {
      console.error('Erro ao buscar métodos de pagamento:', error);
      throw error; // Re-throw to let React Query handle it
    }
  };
  
  // Funções para calcular totais com validação robusta
  const calculateTotals = (transactions: Transaction[]) => {
    // Garantir que transactions é um array válido
    if (!Array.isArray(transactions)) {
      console.error('Erro: transactions não é um array válido', transactions);
      return {
        totalIncome: 0,
        totalExpense: 0,
        balance: 0,
        paymentMethodDistribution: {},
        chartData: []
      };
    }
    
    const totalIncome = transactions
      .filter(t => t && t.type === 'entrada' && !isNaN(Number(t.amount)))
      .reduce((sum, t) => sum + Number(t.amount), 0);
      
    const totalExpense = transactions
      .filter(t => t && t.type === 'saida' && !isNaN(Number(t.amount)))
      .reduce((sum, t) => sum + Number(t.amount), 0);
      
    const balance = totalIncome - totalExpense;
    
    // Calcular distribuição por método de pagamento com validação aprimorada
    const paymentMethodDistribution: Record<string, { amount: number, percentage: number }> = {};
    
    // Primeiro, calcular o total por método de pagamento apenas para entradas
    const incomingTransactions = transactions.filter(t => t && t.type === 'entrada');
    let totalIncomeForMethods = 0;
    
    // Somar o total de entradas para cálculo de percentagem
    incomingTransactions.forEach(transaction => {
      if (transaction?.payment_method?.name && !isNaN(Number(transaction.amount))) {
        totalIncomeForMethods += Number(transaction.amount);
      }
    });
    
    // Calcular montante e percentagem para cada método
    incomingTransactions.forEach(transaction => {
      if (transaction?.payment_method?.name) {
        const methodName = transaction.payment_method.name;
        const amount = Number(transaction.amount || 0);
        
        if (!isNaN(amount)) {
          // Inicializar o método se ainda não existir
          if (!paymentMethodDistribution[methodName]) {
            paymentMethodDistribution[methodName] = { amount: 0, percentage: 0 };
          }
          
          // Adicionar ao total deste método
          paymentMethodDistribution[methodName].amount += amount;
        }
      }
    });
    
    // Calcular as percentagens após ter todos os valores
    Object.keys(paymentMethodDistribution).forEach(methodName => {
      const methodAmount = paymentMethodDistribution[methodName].amount;
      // Calcular percentagem arredondada
      const percentage = totalIncomeForMethods > 0 
        ? Math.round((methodAmount / totalIncomeForMethods) * 100) 
        : 0;
      
      paymentMethodDistribution[methodName].percentage = percentage;
    });
    
    // Calcular totais por dia para o gráfico com tratamento de erros
    const groupByDate: Record<string, { entrada: number; saida: number }> = {};
    
    // Inicializar com os últimos 7 dias para garantir dados mesmo se não houver transações
    const today = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      groupByDate[dateStr] = { entrada: 0, saida: 0 };
    }
    
    // Adicionar dados reais
    transactions.forEach(transaction => {
      try {
        if (!transaction?.transaction_date) return;
        
        const date = new Date(transaction.transaction_date).toISOString().split('T')[0];
        
        if (!groupByDate[date]) {
          groupByDate[date] = { entrada: 0, saida: 0 };
        }
        
        if (transaction.type === 'entrada' || transaction.type === 'saida') {
          const amount = Number(transaction.amount || 0);
          if (!isNaN(amount)) {
            groupByDate[date][transaction.type] += amount;
          }
        }
      } catch (error) {
        console.error('Erro ao processar data da transação:', error);
      }
    });
    
    // Formatar para o gráfico
    const chartData = Object.entries(groupByDate)
      .map(([date, values]) => {
        try {
          const formattedDate = new Date(date);
          // Verificar se a data é válida
          if (isNaN(formattedDate.getTime())) {
            return null;
          }
          
          return {
            name: formattedDate.toLocaleDateString('pt-BR', { weekday: 'short' }),
            entrada: values.entrada,
            saida: values.saida,
            value: values.entrada - values.saida
          };
        } catch (error) {
          console.error('Erro ao formatar data para o gráfico:', error);
          return null;
        }
      })
      .filter(item => item !== null) // Remover itens inválidos
      .sort((a, b) => {
        if (!a || !b) return 0;
        try {
          const dateA = new Date(a.name);
          const dateB = new Date(b.name);
          if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
            return 0;
          }
          return dateA.getTime() - dateB.getTime();
        } catch (error) {
          console.error('Erro ao ordenar datas:', error);
          return 0;
        }
      })
      .slice(-7); // Últimos 7 dias
    
    return { totalIncome, totalExpense, balance, paymentMethodDistribution, chartData };
  };
  
  // Obter o perfil do usuário atual
  const getUserProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;
      
      // Verificar se o perfil existe
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();
        
      if (error) {
        console.error('Erro ao buscar perfil:', error);
        return null;
      }
      
      return profile;
    } catch (error) {
      console.error('Erro ao buscar usuário:', error);
      return null;
    }
  };
  
  // Adicionar transação
  const addTransaction = async (newTransaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      // Verificar se o usuário tem um perfil
      const profile = await getUserProfile();
      
      if (!profile) {
        throw new Error('Perfil de usuário não encontrado. Entre em contato com o administrador.');
      }
      
      const { data, error } = await supabase
        .from('transactions')
        .insert([newTransaction])
        .select();
        
      if (error) {
        console.error('Erro ao adicionar transação:', error);
        throw new Error(error.message);
      }
      
      return data[0];
    } catch (error) {
      console.error('Erro ao adicionar transação:', error);
      throw error;
    }
  };
  
  // Atualizar transação
  const updateTransaction = async (id: string, updates: Partial<Transaction>) => {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .update(updates)
        .eq('id', id)
        .select();
        
      if (error) {
        console.error('Erro ao atualizar transação:', error);
        throw new Error(error.message);
      }
      
      return data[0];
    } catch (error) {
      console.error('Erro ao atualizar transação:', error);
      throw error;
    }
  };
  
  // Excluir transação
  const deleteTransaction = async (id: string) => {
    try {
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('id', id);
        
      if (error) {
        console.error('Erro ao excluir transação:', error);
        throw new Error(error.message);
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao excluir transação:', error);
      throw error;
    }
  };
  
  // Use React Query para gerenciar estado e cache com melhor tratamento de erros
  const transactionsQuery = useQuery({
    queryKey: ['transactions', filters],
    queryFn: fetchTransactions,
    retry: 2,
    refetchOnWindowFocus: false,
    staleTime: 30000, // 30 segundos
    enabled: !!filters, // Only run the query if filters are provided and valid
  });
  
  const paymentMethodsQuery = useQuery({
    queryKey: ['paymentMethods'],
    queryFn: fetchPaymentMethods,
    retry: 2,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minuto
  });
  
  // Mutations
  const addMutation = useMutation({
    mutationFn: addTransaction,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      toast("Transação adicionada", {
        description: "A transação foi adicionada com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao adicionar transação", {
        description: error.message
      });
    },
  });
  
  const updateMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Transaction> }) => 
      updateTransaction(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      toast("Transação atualizada", {
        description: "A transação foi atualizada com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao atualizar transação", {
        description: error.message
      });
    },
  });
  
  const deleteMutation = useMutation({
    mutationFn: deleteTransaction,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      toast("Transação excluída", {
        description: "A transação foi excluída com sucesso."
      });
    },
    onError: (error: Error) => {
      toast.error("Erro ao excluir transação", {
        description: error.message
      });
    },
  });
  
  // Filter transactions based on provided filters
  const filteredTransactions = useMemo(() => {
    if (!transactionsQuery.data) return [];
    
    return transactionsQuery.data.filter(transaction => {
      // Date filter
      if (filters?.startDate && filters?.endDate) {
        const transactionDate = new Date(transaction.transaction_date);
        const startDate = new Date(filters.startDate);
        const endDate = new Date(filters.endDate);
        endDate.setHours(23, 59, 59, 999); // End of day
        
        if (transactionDate < startDate || transactionDate > endDate) {
          return false;
        }
      }
      
      // Type filter
      if (filters?.type && transaction.type !== filters.type) {
        return false;
      }
      
      // Payment method filter (skip if value is _all or null)
      if (filters?.paymentMethod && 
          filters.paymentMethod !== '_all' && 
          transaction.payment_method_id !== filters.paymentMethod) {
        return false;
      }
      
      return true;
    });
  }, [transactionsQuery.data, filters]);
  
  // Calcular totais quando os dados estiverem disponíveis com tratamento de erros
  // Using useMemo to prevent recalculation on every render
  const totals = useMemo(() => {
    try {
      if (transactionsQuery.data) {
        return calculateTotals(filteredTransactions);
      }
      return {
        totalIncome: 0,
        totalExpense: 0,
        balance: 0,
        paymentMethodDistribution: {},
        chartData: []
      };
    } catch (error) {
      console.error('Erro ao calcular totais:', error);
      return {
        totalIncome: 0,
        totalExpense: 0,
        balance: 0,
        paymentMethodDistribution: {},
        chartData: []
      };
    }
  }, [transactionsQuery.data, filteredTransactions]);
  
  // Escutar por mudanças em tempo real
  useEffect(() => {
    const channel = supabase
      .channel('transactions-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'transactions' 
      }, () => {
        // Invalidar a query para recarregar os dados
        queryClient.invalidateQueries({ queryKey: ['transactions'] });
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);
  
  return {
    transactions: transactionsQuery.data || [],
    isLoading: transactionsQuery.isLoading,
    isError: transactionsQuery.isError,
    error: transactionsQuery.error,
    paymentMethods: paymentMethodsQuery.data || [],
    paymentMethodsLoading: paymentMethodsQuery.isLoading,
    totals,
    addTransaction: addMutation.mutate,
    updateTransaction: updateMutation.mutate,
    deleteTransaction: deleteMutation.mutate,
    getUserProfile,
  };
}
