
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.75rem;

    --modal-padding: 1rem; /* Default for mobile */
    --modal-radius: 0.75rem; /* Border radius for modals */

    --oluchys-accent: 210 100% 50%;
    --oluchys-accent-foreground: 0 0% 100%;
    --oluchys-highlight: 200 98% 39%;
    --oluchys-neutral: 220 14% 96%;

    --sidebar-background: 220 33% 99%;
    --sidebar-foreground: 240 5% 34%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 13% 95%;
    --sidebar-accent-foreground: 240 5% 34%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 240 5% 34%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;

    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    --oluchys-accent: 210 100% 50%;
    --oluchys-accent-foreground: 0 0% 100%;
    --oluchys-highlight: 200 98% 39%;
    --oluchys-neutral: 0 0% 15%;

    --sidebar-background: 0 0% 7%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 15%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 0 0% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
  a, button {
    @apply transition-all duration-200;
  }
}

@layer utilities {
  .glass-panel {
    @apply bg-white/70 dark:bg-black/70 backdrop-blur-md border border-white/20 dark:border-white/10;
  }

  .text-balance {
    text-wrap: balance;
  }

  .modal-padding {
    @apply p-4 sm:p-6;
  }

  .modal-content {
    @apply p-4 sm:p-6 overflow-y-auto max-h-[85vh];
    border-radius: var(--modal-radius);
  }

  .modal-form {
    @apply space-y-3 sm:space-y-4 px-0.5;
  }

  .modal-form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4;
  }

  .modal-rounded {
    border-radius: var(--modal-radius);
  }

  .modal-footer {
    @apply flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:space-x-2;
  }
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Set modal padding for larger screens */
@media (min-width: 640px) {
  :root {
    --modal-padding: 1.5rem; /* 24px for sm screens and above */
  }
}
