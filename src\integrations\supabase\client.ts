
// Este arquivo foi modificado para a versão de demonstração.
// Importa o cliente local que simula o Supabase usando localStorage.
import { localSupabase } from '@/lib/localClient';
import { LocalStorageDB } from '@/lib/localStorage';

// Exporta o cliente local como se fosse o Supabase
export const supabase = localSupabase;

// Função para incrementar uma coluna em uma tabela (versão local)
export const incrementColumn = async (
  tableName: "products" | "product_variants" | "sales" | "colors" | "notifications" |
            "profiles" | "payment_methods" | "product_categories" | "sizes" |
            "sale_statuses" | "transactions",
  id: string,
  columnName: string,
  value: number = 1
) => {
  // Mapear nome da tabela para chave de armazenamento
  const storageKeyMapping: Record<string, string> = {
    'products': LocalStorageDB.STORAGE_KEYS.PRODUCTS,
    'product_variants': LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
    'sales': LocalStorageDB.STORAGE_KEYS.SALES,
    'colors': LocalStorageDB.STORAGE_KEYS.COLORS,
    'notifications': LocalStorageDB.STORAGE_KEYS.NOTIFICATIONS,
    'profiles': LocalStorageDB.STORAGE_KEYS.PROFILES,
    'payment_methods': LocalStorageDB.STORAGE_KEYS.PAYMENT_METHODS,
    'product_categories': LocalStorageDB.STORAGE_KEYS.CATEGORIES,
    'sizes': LocalStorageDB.STORAGE_KEYS.SIZES,
    'sale_statuses': LocalStorageDB.STORAGE_KEYS.SALE_STATUSES,
    'transactions': LocalStorageDB.STORAGE_KEYS.TRANSACTIONS
  };

  const storageKey = storageKeyMapping[tableName] || tableName;

  // Buscar o item atual
  const { data, error } = await LocalStorageDB.findById(storageKey, id);

  if (error) {
    console.error(`Erro ao buscar ${columnName} em ${tableName}:`, error);
    throw error;
  }

  if (!data) {
    throw new Error(`Item com ID ${id} não encontrado em ${tableName}`);
  }

  // Calcular o novo valor
  const currentValue = data[columnName] || 0;
  const newValue = currentValue + value;

  // Atualizar o item
  const { error: updateError } = await LocalStorageDB.update(
    storageKey,
    id,
    { [columnName]: newValue }
  );

  if (updateError) {
    console.error(`Erro ao incrementar ${columnName} em ${tableName}:`, updateError);
    throw updateError;
  }

  return newValue;
};

// Função para decrementar uma coluna em uma tabela (versão local)
export const decrementColumn = async (
  tableName: "products" | "product_variants" | "sales" | "colors" | "notifications" |
            "profiles" | "payment_methods" | "product_categories" | "sizes" |
            "sale_statuses" | "transactions",
  id: string,
  columnName: string,
  value: number = 1
) => {
  // Mapear nome da tabela para chave de armazenamento
  const storageKeyMapping: Record<string, string> = {
    'products': LocalStorageDB.STORAGE_KEYS.PRODUCTS,
    'product_variants': LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
    'sales': LocalStorageDB.STORAGE_KEYS.SALES,
    'colors': LocalStorageDB.STORAGE_KEYS.COLORS,
    'notifications': LocalStorageDB.STORAGE_KEYS.NOTIFICATIONS,
    'profiles': LocalStorageDB.STORAGE_KEYS.PROFILES,
    'payment_methods': LocalStorageDB.STORAGE_KEYS.PAYMENT_METHODS,
    'product_categories': LocalStorageDB.STORAGE_KEYS.CATEGORIES,
    'sizes': LocalStorageDB.STORAGE_KEYS.SIZES,
    'sale_statuses': LocalStorageDB.STORAGE_KEYS.SALE_STATUSES,
    'transactions': LocalStorageDB.STORAGE_KEYS.TRANSACTIONS
  };

  const storageKey = storageKeyMapping[tableName] || tableName;

  // Buscar o item atual
  const { data, error } = await LocalStorageDB.findById(storageKey, id);

  if (error) {
    console.error(`Erro ao buscar ${columnName} em ${tableName}:`, error);
    throw error;
  }

  if (!data) {
    throw new Error(`Item com ID ${id} não encontrado em ${tableName}`);
  }

  // Calcular o novo valor (garantir que não fique negativo)
  const currentValue = data[columnName] || 0;
  const newValue = Math.max(0, currentValue - value);

  // Atualizar o item
  const { error: updateError } = await LocalStorageDB.update(
    storageKey,
    id,
    { [columnName]: newValue }
  );

  if (updateError) {
    console.error(`Erro ao decrementar ${columnName} em ${tableName}:`, updateError);
    throw updateError;
  }

  return newValue;
};
