
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ffirmjpvmughvtiawewp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZmaXJtanB2bXVnaHZ0aWF3ZXdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NDM5NTksImV4cCI6MjA1ODIxOTk1OX0.tRn9e9wnqiIKr1Vs2nkSlGLn3Rn41gmjS5bXh-ZD78Q";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Função para incrementar uma coluna em uma tabela
export const incrementColumn = async (
  tableName: "products" | "product_variants" | "sales" | "colors" | "notifications" | 
            "profiles" | "payment_methods" | "product_categories" | "sizes" | 
            "sale_statuses" | "transactions", 
  id: string, 
  columnName: string, 
  value: number = 1
) => {
  // Primeiro, buscar o valor atual
  const { data, error } = await supabase
    .from(tableName)
    .select(columnName)
    .eq('id', id)
    .single();
    
  if (error) {
    console.error(`Erro ao buscar ${columnName} em ${tableName}:`, error);
    throw error;
  }
  
  // Calcular o novo valor
  const currentValue = data ? (data[columnName] || 0) : 0;
  const newValue = currentValue + value;
  
  // Atualizar a coluna com o novo valor
  const { error: updateError } = await supabase
    .from(tableName)
    .update({ [columnName]: newValue })
    .eq('id', id);
    
  if (updateError) {
    console.error(`Erro ao incrementar ${columnName} em ${tableName}:`, updateError);
    throw updateError;
  }
  
  return newValue;
};

// Função para decrementar uma coluna em uma tabela
export const decrementColumn = async (
  tableName: "products" | "product_variants" | "sales" | "colors" | "notifications" | 
            "profiles" | "payment_methods" | "product_categories" | "sizes" | 
            "sale_statuses" | "transactions", 
  id: string, 
  columnName: string, 
  value: number = 1
) => {
  // Primeiro, buscar o valor atual
  const { data, error } = await supabase
    .from(tableName)
    .select(columnName)
    .eq('id', id)
    .single();
    
  if (error) {
    console.error(`Erro ao buscar ${columnName} em ${tableName}:`, error);
    throw error;
  }
  
  // Calcular o novo valor (garantir que não fique negativo)
  const currentValue = data ? (data[columnName] || 0) : 0;
  const newValue = Math.max(0, currentValue - value);
  
  // Atualizar a coluna com o novo valor
  const { error: updateError } = await supabase
    .from(tableName)
    .update({ [columnName]: newValue })
    .eq('id', id);
    
  if (updateError) {
    console.error(`Erro ao decrementar ${columnName} em ${tableName}:`, updateError);
    throw updateError;
  }
  
  return newValue;
};
