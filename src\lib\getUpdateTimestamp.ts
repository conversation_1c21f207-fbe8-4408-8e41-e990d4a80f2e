/**
 * Gera um timestamp formatado para o arquivo de atualizações
 * no formato "## YYYY-MM-DD (HH:MM)"
 * 
 * @returns {string} Timestamp formatado
 */
export function getUpdateTimestamp(): string {
  const now = new Date();
  
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  return `## ${year}-${month}-${day} (${hours}:${minutes})`;
}
