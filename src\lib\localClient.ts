/**
 * @file localClient.ts
 * @description Cliente local que simula as operações do Supabase para a versão de demonstração.
 * Fornece uma interface compatível com o cliente Supabase original.
 */

import { LocalStorageDB } from './localStorage';
import { Profile } from '@/types';

// Interface para simular o usuário do Supabase
interface LocalUser {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

// Interface para simular a sessão do Supabase
interface LocalSession {
  user: LocalUser;
  access_token: string;
  refresh_token: string;
  expires_at: number;
}

// Interface para resposta de autenticação
interface AuthResponse {
  data: {
    user: LocalUser | null;
    session: LocalSession | null;
  };
  error: Error | null;
}

// Interface para resposta de dados
interface DataResponse<T> {
  data: T | null;
  error: Error | null;
}

// Interface para resposta de lista
interface ListResponse<T> {
  data: T[];
  error: Error | null;
}

/**
 * Classe que simula o cliente Supabase para operações locais
 */
class LocalSupabaseClient {
  
  /**
   * Simula o objeto auth do Supabase
   */
  auth = {
    /**
     * Realiza login com email e senha
     */
    signInWithPassword: async (credentials: { email: string; password: string }): Promise<AuthResponse> => {
      try {
        const profileResponse = await LocalStorageDB.signIn(credentials.email, credentials.password);
        
        if (profileResponse.error || !profileResponse.data) {
          return {
            data: { user: null, session: null },
            error: profileResponse.error || new Error('Falha na autenticação')
          };
        }

        const profile = profileResponse.data;
        
        // Criar usuário simulado
        const user: LocalUser = {
          id: profile.id,
          email: credentials.email,
          created_at: profile.created_at || new Date().toISOString(),
          updated_at: profile.updated_at || new Date().toISOString()
        };

        // Criar sessão simulada
        const session: LocalSession = {
          user,
          access_token: `demo_token_${profile.id}`,
          refresh_token: `demo_refresh_${profile.id}`,
          expires_at: Date.now() + (24 * 60 * 60 * 1000) // 24 horas
        };

        // Salvar sessão no localStorage
        localStorage.setItem('demo_session', JSON.stringify(session));

        return {
          data: { user, session },
          error: null
        };
      } catch (error) {
        return {
          data: { user: null, session: null },
          error: error as Error
        };
      }
    },

    /**
     * Realiza logout
     */
    signOut: async (): Promise<{ error: Error | null }> => {
      try {
        await LocalStorageDB.signOut();
        localStorage.removeItem('demo_session');
        
        return { error: null };
      } catch (error) {
        return { error: error as Error };
      }
    },

    /**
     * Obtém a sessão atual
     */
    getSession: async (): Promise<{ data: { session: LocalSession | null } }> => {
      try {
        const sessionData = localStorage.getItem('demo_session');
        
        if (!sessionData) {
          return { data: { session: null } };
        }

        const session: LocalSession = JSON.parse(sessionData);
        
        // Verificar se a sessão não expirou
        if (session.expires_at < Date.now()) {
          localStorage.removeItem('demo_session');
          return { data: { session: null } };
        }

        return { data: { session } };
      } catch (error) {
        return { data: { session: null } };
      }
    },

    /**
     * Simula o listener de mudanças de autenticação
     */
    onAuthStateChange: (callback: (event: string, session: LocalSession | null) => void) => {
      // Verificar sessão atual
      const checkSession = async () => {
        const { data } = await this.auth.getSession();
        callback('INITIAL_SESSION', data.session);
      };

      checkSession();

      // Simular listener (em uma implementação real, isso seria mais sofisticado)
      const interval = setInterval(async () => {
        const { data } = await this.auth.getSession();
        // Só chama o callback se houve mudança na sessão
        // (implementação simplificada)
      }, 5000);

      return {
        data: {
          subscription: {
            unsubscribe: () => {
              clearInterval(interval);
            }
          }
        }
      };
    }
  };

  /**
   * Simula operações de banco de dados
   */
  from(table: string) {
    return {
      /**
       * Seleciona campos
       */
      select: (fields: string = '*') => ({
        /**
         * Filtra por igualdade
         */
        eq: (column: string, value: any) => ({
          /**
           * Retorna um único resultado
           */
          single: async (): Promise<DataResponse<any>> => {
            try {
              const storageKey = this.getStorageKey(table);
              const response = await LocalStorageDB.findWhere(storageKey, (item: any) => item[column] === value);
              
              if (response.error) {
                return { data: null, error: response.error };
              }

              const item = response.data[0] || null;
              return { data: item, error: null };
            } catch (error) {
              return { data: null, error: error as Error };
            }
          },

          /**
           * Retorna múltiplos resultados
           */
          then: async (callback: (response: ListResponse<any>) => void) => {
            try {
              const storageKey = this.getStorageKey(table);
              const response = await LocalStorageDB.findWhere(storageKey, (item: any) => item[column] === value);
              callback(response);
            } catch (error) {
              callback({ data: [], error: error as Error });
            }
          }
        }),

        /**
         * Executa a consulta e retorna todos os resultados
         */
        then: async (callback: (response: ListResponse<any>) => void) => {
          try {
            const storageKey = this.getStorageKey(table);
            const response = await LocalStorageDB.findAll(storageKey);
            callback(response);
          } catch (error) {
            callback({ data: [], error: error as Error });
          }
        }
      }),

      /**
       * Insere novos dados
       */
      insert: (data: any) => ({
        select: () => ({
          single: async (): Promise<DataResponse<any>> => {
            try {
              const storageKey = this.getStorageKey(table);
              const response = await LocalStorageDB.create(storageKey, data);
              return response;
            } catch (error) {
              return { data: null, error: error as Error };
            }
          }
        })
      }),

      /**
       * Atualiza dados existentes
       */
      update: (data: any) => ({
        eq: (column: string, value: any) => ({
          select: () => ({
            single: async (): Promise<DataResponse<any>> => {
              try {
                const storageKey = this.getStorageKey(table);
                const response = await LocalStorageDB.update(storageKey, value, data);
                return response;
              } catch (error) {
                return { data: null, error: error as Error };
              }
            }
          })
        })
      }),

      /**
       * Remove dados
       */
      delete: () => ({
        eq: (column: string, value: any) => ({
          then: async (callback: (response: { error: Error | null }) => void) => {
            try {
              const storageKey = this.getStorageKey(table);
              const response = await LocalStorageDB.delete(storageKey, value);
              callback({ error: response.error });
            } catch (error) {
              callback({ error: error as Error });
            }
          }
        })
      })
    };
  }

  /**
   * Mapeia nomes de tabelas para chaves de armazenamento
   */
  private getStorageKey(table: string): string {
    const mapping: Record<string, string> = {
      'profiles': LocalStorageDB.STORAGE_KEYS.PROFILES,
      'products': LocalStorageDB.STORAGE_KEYS.PRODUCTS,
      'product_variants': LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
      'sales': LocalStorageDB.STORAGE_KEYS.SALES,
      'transactions': LocalStorageDB.STORAGE_KEYS.TRANSACTIONS,
      'payment_methods': LocalStorageDB.STORAGE_KEYS.PAYMENT_METHODS,
      'product_categories': LocalStorageDB.STORAGE_KEYS.CATEGORIES,
      'colors': LocalStorageDB.STORAGE_KEYS.COLORS,
      'sizes': LocalStorageDB.STORAGE_KEYS.SIZES,
      'sale_statuses': LocalStorageDB.STORAGE_KEYS.SALE_STATUSES,
      'delivery_persons': LocalStorageDB.STORAGE_KEYS.DELIVERY_PERSONS,
      'notifications': LocalStorageDB.STORAGE_KEYS.NOTIFICATIONS
    };

    return mapping[table] || table;
  }
}

// Instância única do cliente local
export const localSupabase = new LocalSupabaseClient();

// Função para inicializar o banco de dados local
export const initializeLocalDatabase = async () => {
  await LocalStorageDB.initializeDatabase();
};
