/**
 * @file localStorage.ts
 * @description Sistema de armazenamento local que simula as operações do Supabase
 * usando localStorage do navegador. Fornece funcionalidades CRUD, relacionamentos
 * e validações para a versão de demonstração do sistema.
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  Profile, 
  Product, 
  ProductVariant, 
  Sale, 
  Transaction, 
  PaymentMethod, 
  ProductCategory, 
  Color, 
  Size, 
  SaleStatus, 
  DeliveryPerson,
  Notification 
} from '@/types';

// Chaves para localStorage
const STORAGE_KEYS = {
  PROFILES: 'demo_profiles',
  PRODUCTS: 'demo_products',
  PRODUCT_VARIANTS: 'demo_product_variants',
  SALES: 'demo_sales',
  TRANSACTIONS: 'demo_transactions',
  PAYMENT_METHODS: 'demo_payment_methods',
  CATEGORIES: 'demo_categories',
  COLORS: 'demo_colors',
  SIZES: 'demo_sizes',
  SALE_STATUSES: 'demo_sale_statuses',
  DELIVERY_PERSONS: 'demo_delivery_persons',
  NOTIFICATIONS: 'demo_notifications',
  CURRENT_USER: 'demo_current_user',
  INITIALIZED: 'demo_initialized'
};

// Interface para resposta simulada do Supabase
interface LocalStorageResponse<T> {
  data: T | null;
  error: Error | null;
}

// Interface para resposta de lista
interface LocalStorageListResponse<T> {
  data: T[];
  error: Error | null;
}

/**
 * Classe principal para gerenciar o armazenamento local
 */
export class LocalStorageDB {
  
  /**
   * Salva dados no localStorage
   */
  private static setItem<T>(key: string, data: T[]): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error(`Erro ao salvar ${key}:`, error);
    }
  }

  /**
   * Recupera dados do localStorage
   */
  private static getItem<T>(key: string): T[] {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Erro ao recuperar ${key}:`, error);
      return [];
    }
  }

  /**
   * Gera timestamp atual no formato ISO
   */
  private static getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Simula delay de rede para tornar mais realista
   */
  private static async simulateNetworkDelay(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 100));
  }

  /**
   * Inicializa o banco de dados com dados de demonstração
   */
  static async initializeDatabase(): Promise<void> {
    const isInitialized = localStorage.getItem(STORAGE_KEYS.INITIALIZED);
    
    if (isInitialized) {
      return; // Já foi inicializado
    }

    // Dados iniciais
    await this.initializePaymentMethods();
    await this.initializeCategories();
    await this.initializeColors();
    await this.initializeSizes();
    await this.initializeSaleStatuses();
    await this.initializeDeliveryPersons();
    await this.initializeProfiles();
    await this.initializeProducts();
    await this.initializeProductVariants();
    await this.initializeSales();
    await this.initializeTransactions();
    await this.initializeNotifications();

    localStorage.setItem(STORAGE_KEYS.INITIALIZED, 'true');
  }

  /**
   * Limpa todos os dados do localStorage
   */
  static clearAllData(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // ==================== MÉTODOS GENÉRICOS CRUD ====================

  /**
   * Busca um item por ID
   */
  static async findById<T extends { id: string }>(
    storageKey: string, 
    id: string
  ): Promise<LocalStorageResponse<T>> {
    await this.simulateNetworkDelay();
    
    try {
      const items = this.getItem<T>(storageKey);
      const item = items.find(item => item.id === id);
      
      return {
        data: item || null,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Busca todos os itens
   */
  static async findAll<T>(storageKey: string): Promise<LocalStorageListResponse<T>> {
    await this.simulateNetworkDelay();
    
    try {
      const items = this.getItem<T>(storageKey);
      
      return {
        data: items,
        error: null
      };
    } catch (error) {
      return {
        data: [],
        error: error as Error
      };
    }
  }

  /**
   * Cria um novo item
   */
  static async create<T extends { id: string; created_at?: string; updated_at?: string }>(
    storageKey: string, 
    newItem: Omit<T, 'id' | 'created_at' | 'updated_at'>
  ): Promise<LocalStorageResponse<T>> {
    await this.simulateNetworkDelay();
    
    try {
      const items = this.getItem<T>(storageKey);
      const timestamp = this.getCurrentTimestamp();
      
      const item: T = {
        ...newItem,
        id: uuidv4(),
        created_at: timestamp,
        updated_at: timestamp
      } as T;
      
      items.push(item);
      this.setItem(storageKey, items);
      
      return {
        data: item,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Atualiza um item existente
   */
  static async update<T extends { id: string; updated_at?: string }>(
    storageKey: string,
    id: string,
    updates: Partial<T>
  ): Promise<LocalStorageResponse<T>> {
    await this.simulateNetworkDelay();

    try {
      const items = this.getItem<T>(storageKey);
      const index = items.findIndex(item => item.id === id);

      if (index === -1) {
        return {
          data: null,
          error: new Error(`Item com ID ${id} não encontrado`)
        };
      }

      const updatedItem = {
        ...items[index],
        ...updates,
        updated_at: this.getCurrentTimestamp()
      };

      items[index] = updatedItem;
      this.setItem(storageKey, items);

      return {
        data: updatedItem,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Remove um item
   */
  static async delete<T extends { id: string }>(
    storageKey: string,
    id: string
  ): Promise<LocalStorageResponse<null>> {
    await this.simulateNetworkDelay();

    try {
      const items = this.getItem<T>(storageKey);
      const index = items.findIndex(item => item.id === id);

      if (index === -1) {
        return {
          data: null,
          error: new Error(`Item com ID ${id} não encontrado`)
        };
      }

      items.splice(index, 1);
      this.setItem(storageKey, items);

      return {
        data: null,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error as Error
      };
    }
  }

  /**
   * Busca itens com filtro
   */
  static async findWhere<T>(
    storageKey: string,
    filter: (item: T) => boolean
  ): Promise<LocalStorageListResponse<T>> {
    await this.simulateNetworkDelay();

    try {
      const items = this.getItem<T>(storageKey);
      const filteredItems = items.filter(filter);

      return {
        data: filteredItems,
        error: null
      };
    } catch (error) {
      return {
        data: [],
        error: error as Error
      };
    }
  }

  // ==================== MÉTODOS ESPECÍFICOS POR ENTIDADE ====================

  // ----- PROFILES (USUÁRIOS) -----

  /**
   * Inicializa perfis de usuários de demonstração
   */
  private static async initializeProfiles(): Promise<void> {
    const profiles: Profile[] = [
      {
        id: uuidv4(),
        name: 'Administrador',
        phone: '(11) 98765-4321',
        role: 'admin',
        active: true,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Vendedor Demo',
        phone: '(11) 91234-5678',
        role: 'vendedor',
        active: true,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.PROFILES, profiles);
  }

  /**
   * Busca perfil por email (simulado)
   */
  static async findProfileByEmail(email: string): Promise<LocalStorageResponse<Profile>> {
    await this.simulateNetworkDelay();

    const profiles = await this.findAll<Profile>(STORAGE_KEYS.PROFILES);

    // Mapeamento fixo de emails para perfis
    const emailToRole: Record<string, 'admin' | 'vendedor'> = {
      '<EMAIL>': 'admin',
      '<EMAIL>': 'vendedor'
    };

    const role = emailToRole[email];

    if (!role) {
      return {
        data: null,
        error: new Error('Usuário não encontrado')
      };
    }

    const profile = profiles.data.find(p => p.role === role);

    return {
      data: profile || null,
      error: profile ? null : new Error('Perfil não encontrado')
    };
  }

  /**
   * Verifica credenciais de login
   */
  static async signIn(email: string, password: string): Promise<LocalStorageResponse<Profile>> {
    await this.simulateNetworkDelay();

    // Credenciais fixas para demonstração
    const validCredentials = [
      { email: '<EMAIL>', password: '123', role: 'admin' },
      { email: '<EMAIL>', password: '123', role: 'vendedor' }
    ];

    const isValid = validCredentials.some(cred =>
      cred.email === email && cred.password === password
    );

    if (!isValid) {
      return {
        data: null,
        error: new Error('Credenciais inválidas')
      };
    }

    const profile = await this.findProfileByEmail(email);

    if (profile.data) {
      // Salva usuário atual no localStorage
      localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(profile.data));
    }

    return profile;
  }

  /**
   * Realiza logout
   */
  static async signOut(): Promise<void> {
    await this.simulateNetworkDelay();
    localStorage.removeItem(STORAGE_KEYS.CURRENT_USER);
  }

  /**
   * Obtém usuário atual
   */
  static getCurrentUser(): Profile | null {
    const userData = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);
    return userData ? JSON.parse(userData) : null;
  }

  // ----- PAYMENT METHODS (MÉTODOS DE PAGAMENTO) -----

  /**
   * Inicializa métodos de pagamento de demonstração
   */
  private static async initializePaymentMethods(): Promise<void> {
    const paymentMethods: PaymentMethod[] = [
      {
        id: uuidv4(),
        name: 'Dinheiro'
      },
      {
        id: uuidv4(),
        name: 'Cartão de Crédito'
      },
      {
        id: uuidv4(),
        name: 'Cartão de Débito'
      },
      {
        id: uuidv4(),
        name: 'PIX'
      },
      {
        id: uuidv4(),
        name: 'Transferência Bancária'
      }
    ];

    this.setItem(STORAGE_KEYS.PAYMENT_METHODS, paymentMethods);
  }

  // ----- CATEGORIES (CATEGORIAS DE PRODUTOS) -----

  /**
   * Inicializa categorias de produtos de demonstração
   */
  private static async initializeCategories(): Promise<void> {
    const categories: ProductCategory[] = [
      {
        id: uuidv4(),
        name: 'Camisetas',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Calças',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Vestidos',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Acessórios',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Calçados',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.CATEGORIES, categories);
  }

  // ----- COLORS (CORES) -----

  /**
   * Inicializa cores de demonstração
   */
  private static async initializeColors(): Promise<void> {
    const colors: Color[] = [
      {
        id: uuidv4(),
        name: 'Preto',
        hex_code: '#000000',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Branco',
        hex_code: '#FFFFFF',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Vermelho',
        hex_code: '#FF0000',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Azul',
        hex_code: '#0000FF',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Verde',
        hex_code: '#00FF00',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.COLORS, colors);
  }

  // ----- SIZES (TAMANHOS) -----

  /**
   * Inicializa tamanhos de demonstração
   */
  private static async initializeSizes(): Promise<void> {
    const sizes: Size[] = [
      {
        id: uuidv4(),
        name: 'P',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'M',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'G',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'GG',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'XG',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.SIZES, sizes);
  }

  // ----- SALE STATUSES (STATUS DE VENDAS) -----

  /**
   * Inicializa status de vendas de demonstração
   */
  private static async initializeSaleStatuses(): Promise<void> {
    const statuses: SaleStatus[] = [
      {
        id: uuidv4(),
        name: 'Pendente',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Pago',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Enviado',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Entregue',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Cancelado',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.SALE_STATUSES, statuses);
  }

  // ----- DELIVERY PERSONS (ENTREGADORES) -----

  /**
   * Inicializa entregadores de demonstração
   */
  private static async initializeDeliveryPersons(): Promise<void> {
    const deliveryPersons: DeliveryPerson[] = [
      {
        id: uuidv4(),
        name: 'João Silva',
        phone: '(11) 98765-1234',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Maria Oliveira',
        phone: '(11) 97654-3210',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Carlos Santos',
        phone: '(11) 91234-5678',
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.DELIVERY_PERSONS, deliveryPersons);
  }

  // ----- PRODUCTS (PRODUTOS) -----

  /**
   * Inicializa produtos de demonstração
   */
  private static async initializeProducts(): Promise<void> {
    // Buscar categorias
    const categoriesResponse = await this.findAll<ProductCategory>(STORAGE_KEYS.CATEGORIES);
    const categories = categoriesResponse.data;

    if (categories.length === 0) return;

    const getRandomCategory = () => categories[Math.floor(Math.random() * categories.length)];

    const products: Product[] = [
      {
        id: uuidv4(),
        name: 'Camiseta Básica',
        category_id: categories.find(c => c.name === 'Camisetas')?.id || getRandomCategory().id,
        price: 39.90,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Calça Jeans',
        category_id: categories.find(c => c.name === 'Calças')?.id || getRandomCategory().id,
        price: 89.90,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Vestido Floral',
        category_id: categories.find(c => c.name === 'Vestidos')?.id || getRandomCategory().id,
        price: 119.90,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Tênis Casual',
        category_id: categories.find(c => c.name === 'Calçados')?.id || getRandomCategory().id,
        price: 149.90,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        name: 'Colar Prata',
        category_id: categories.find(c => c.name === 'Acessórios')?.id || getRandomCategory().id,
        price: 59.90,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.PRODUCTS, products);
  }

  // ----- PRODUCT VARIANTS (VARIANTES DE PRODUTOS) -----

  /**
   * Inicializa variantes de produtos de demonstração
   */
  private static async initializeProductVariants(): Promise<void> {
    const productsResponse = await this.findAll<Product>(STORAGE_KEYS.PRODUCTS);
    const colorsResponse = await this.findAll<Color>(STORAGE_KEYS.COLORS);
    const sizesResponse = await this.findAll<Size>(STORAGE_KEYS.SIZES);

    const products = productsResponse.data;
    const colors = colorsResponse.data;
    const sizes = sizesResponse.data;

    if (products.length === 0 || colors.length === 0 || sizes.length === 0) return;

    const variants: ProductVariant[] = [];

    // Criar variantes para cada produto
    products.forEach(product => {
      // Criar algumas variantes aleatórias para cada produto
      const numVariants = Math.floor(Math.random() * 3) + 2; // 2-4 variantes por produto

      for (let i = 0; i < numVariants; i++) {
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
        const randomQuantity = Math.floor(Math.random() * 50) + 1; // 1-50 unidades

        variants.push({
          id: uuidv4(),
          product_id: product.id,
          color_id: randomColor.id,
          size_id: randomSize.id,
          quantity: randomQuantity,
          created_at: this.getCurrentTimestamp(),
          updated_at: this.getCurrentTimestamp()
        });
      }
    });

    this.setItem(STORAGE_KEYS.PRODUCT_VARIANTS, variants);
  }

  // ----- SALES (VENDAS) -----

  /**
   * Inicializa vendas de demonstração
   */
  private static async initializeSales(): Promise<void> {
    const variantsResponse = await this.findAll<ProductVariant>(STORAGE_KEYS.PRODUCT_VARIANTS);
    const paymentMethodsResponse = await this.findAll<PaymentMethod>(STORAGE_KEYS.PAYMENT_METHODS);
    const statusesResponse = await this.findAll<SaleStatus>(STORAGE_KEYS.SALE_STATUSES);
    const profilesResponse = await this.findAll<Profile>(STORAGE_KEYS.PROFILES);
    const deliveryPersonsResponse = await this.findAll<DeliveryPerson>(STORAGE_KEYS.DELIVERY_PERSONS);

    const variants = variantsResponse.data;
    const paymentMethods = paymentMethodsResponse.data;
    const statuses = statusesResponse.data;
    const profiles = profilesResponse.data;
    const deliveryPersons = deliveryPersonsResponse.data;

    if (variants.length === 0 || paymentMethods.length === 0 || statuses.length === 0 || profiles.length === 0) return;

    const sales: Sale[] = [];
    const customerNames = [
      'Ana Silva', 'Carlos Oliveira', 'Maria Santos', 'João Pereira', 'Fernanda Costa',
      'Ricardo Lima', 'Juliana Alves', 'Pedro Rodrigues', 'Camila Ferreira', 'Lucas Martins'
    ];

    // Criar algumas vendas de demonstração
    for (let i = 0; i < 15; i++) {
      const randomVariant = variants[Math.floor(Math.random() * variants.length)];
      const randomPaymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      const randomSeller = profiles[Math.floor(Math.random() * profiles.length)];
      const randomDeliveryPerson = Math.random() > 0.5 ? deliveryPersons[Math.floor(Math.random() * deliveryPersons.length)] : undefined;
      const randomCustomer = customerNames[Math.floor(Math.random() * customerNames.length)];

      // Gerar data aleatória nos últimos 30 dias
      const randomDate = new Date();
      randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 30));

      sales.push({
        id: uuidv4(),
        sale_number: 1000 + i,
        customer_name: randomCustomer,
        customer_contact: `(11) 9${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`,
        product_variant_id: randomVariant.id,
        price: Math.floor(Math.random() * 200) + 30, // Preço entre 30 e 230
        payment_method_id: randomPaymentMethod.id,
        notes: Math.random() > 0.7 ? 'Observações da venda' : undefined,
        seller_id: randomSeller.id,
        delivery_person_id: randomDeliveryPerson?.id,
        status_id: randomStatus.id,
        sale_date: randomDate.toISOString(),
        created_at: randomDate.toISOString(),
        updated_at: randomDate.toISOString()
      });
    }

    this.setItem(STORAGE_KEYS.SALES, sales);
  }

  // ----- TRANSACTIONS (TRANSAÇÕES) -----

  /**
   * Inicializa transações de demonstração
   */
  private static async initializeTransactions(): Promise<void> {
    const salesResponse = await this.findAll<Sale>(STORAGE_KEYS.SALES);
    const paymentMethodsResponse = await this.findAll<PaymentMethod>(STORAGE_KEYS.PAYMENT_METHODS);
    const profilesResponse = await this.findAll<Profile>(STORAGE_KEYS.PROFILES);

    const sales = salesResponse.data;
    const paymentMethods = paymentMethodsResponse.data;
    const profiles = profilesResponse.data;

    if (sales.length === 0 || paymentMethods.length === 0 || profiles.length === 0) return;

    const transactions: Transaction[] = [];

    // Criar transações baseadas nas vendas
    sales.forEach(sale => {
      transactions.push({
        id: uuidv4(),
        amount: sale.price,
        type: 'entrada',
        description: `Venda #${sale.sale_number} - ${sale.customer_name}`,
        category: 'Vendas',
        sale_id: sale.id,
        payment_method_id: sale.payment_method_id,
        user_id: sale.seller_id,
        transaction_date: sale.sale_date,
        created_at: sale.created_at,
        updated_at: sale.updated_at
      });
    });

    // Adicionar algumas transações de saída
    const expenseCategories = ['Fornecedores', 'Aluguel', 'Energia', 'Internet', 'Marketing'];

    for (let i = 0; i < 8; i++) {
      const randomDate = new Date();
      randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 30));
      const randomUser = profiles[Math.floor(Math.random() * profiles.length)];
      const randomPaymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
      const randomCategory = expenseCategories[Math.floor(Math.random() * expenseCategories.length)];

      transactions.push({
        id: uuidv4(),
        amount: Math.floor(Math.random() * 500) + 50, // Entre 50 e 550
        type: 'saida',
        description: `Despesa - ${randomCategory}`,
        category: randomCategory,
        payment_method_id: randomPaymentMethod.id,
        user_id: randomUser.id,
        transaction_date: randomDate.toISOString(),
        created_at: randomDate.toISOString(),
        updated_at: randomDate.toISOString()
      });
    }

    this.setItem(STORAGE_KEYS.TRANSACTIONS, transactions);
  }

  // ----- NOTIFICATIONS (NOTIFICAÇÕES) -----

  /**
   * Inicializa notificações de demonstração
   */
  private static async initializeNotifications(): Promise<void> {
    const profilesResponse = await this.findAll<Profile>(STORAGE_KEYS.PROFILES);
    const profiles = profilesResponse.data;

    if (profiles.length === 0) return;

    const notifications: Notification[] = [
      {
        id: uuidv4(),
        message: 'Nova venda realizada com sucesso!',
        type: 'success',
        read: false,
        user_id: profiles[0]?.id,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        message: 'Estoque baixo para alguns produtos',
        type: 'warning',
        read: false,
        user_id: profiles[0]?.id,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      },
      {
        id: uuidv4(),
        message: 'Relatório mensal disponível',
        type: 'info',
        read: true,
        user_id: profiles[0]?.id,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      }
    ];

    this.setItem(STORAGE_KEYS.NOTIFICATIONS, notifications);
  }

  // ==================== MÉTODOS DE ACESSO PÚBLICO ====================

  /**
   * Exporta as chaves de armazenamento para uso externo
   */
  static get STORAGE_KEYS() {
    return STORAGE_KEYS;
  }
}
