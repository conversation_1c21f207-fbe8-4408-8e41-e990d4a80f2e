/**
 * Função para paginar um array de dados
 * @param data Array de dados a ser paginado
 * @param page Número da página atual (começando em 1)
 * @param pageSize Tamanho da página (número de itens por página)
 * @returns Objeto contendo os itens da página atual e informações de paginação
 */
export function paginateData<T>(
  data: T[],
  page: number,
  pageSize: number
): {
  paginatedData: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
} {
  try {
    // Verificar se os dados são válidos
    if (!Array.isArray(data)) {
      console.error('paginateData: data não é um array', data);
      return {
        paginatedData: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
      };
    }

    // Garantir que a página seja pelo menos 1
    const currentPage = Math.max(1, page);

    // Calcular o total de páginas
    const totalItems = data.length;
    const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

    // Ajustar a página atual se estiver fora dos limites
    const validPage = Math.min(currentPage, totalPages);

    // Calcular os índices de início e fim
    const startIndex = (validPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalItems);

    // Extrair os dados da página atual
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      paginatedData,
      totalItems,
      totalPages,
      currentPage: validPage,
    };
  } catch (error) {
    console.error('Erro ao paginar dados:', error);
    return {
      paginatedData: [],
      totalItems: 0,
      totalPages: 1,
      currentPage: 1,
    };
  }
}
