
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formata um número para moeda (kz)
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('pt-AO', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount) + ' kz';
}

/**
 * Converte uma string de data para um formato legível em pt-BR
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('pt-BR');
}

/**
 * Gera uma cor de fundo baseada no nome do status
 */
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    'Realizado': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'Reservado': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'Cancelado': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'Em Andamento': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    'Pendente': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
  };
  
  return statusColors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
}

/**
 * Formata um número com preenchimento de zeros à esquerda
 */
export function formatNumberWithLeadingZeros(number: number, length: number = 4): string {
  return number.toString().padStart(length, '0');
}
