/**
 * @file CashFlow.tsx
 * @description Página de fluxo de caixa que permite visualizar, adicionar, editar e filtrar
 * transações financeiras. Exibe gráficos e métricas de entradas, saídas e saldo,
 * além de uma tabela detalhada com todas as transações.
 */

import React, { useState, useEffect, useMemo } from "react";
import { ArrowDown, ArrowUp, Calendar, CreditCard, DollarSign, Filter, Wallet, Edit, Trash2, Plus, Upload, Download, Loader2, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import DashboardCard from "@/components/DashboardCard";
import DashboardChart from "@/components/DashboardChart";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useForm } from "react-hook-form";
import { useTransactions } from "@/hooks/use-transactions";
import { useAuth } from "@/context/AuthContext";
import { Transaction } from "@/types";
import { formatCurrency } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import DataTablePagination from "@/components/ui/data-table-pagination";
import { paginateData } from "@/lib/pagination";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
const CashFlow = () => {
  const {
    user
  } = useAuth();
  const [dateFilter, setDateFilter] = useState(() => {
    try {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      return {
        startDate: firstDay.toISOString().split('T')[0],
        endDate: lastDay.toISOString().split('T')[0]
      };
    } catch (error) {
      console.error("Erro ao configurar filtro de data:", error);
      return {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      };
    }
  });
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<string | null>(null);
  const [showNewTransactionDialog, setShowNewTransactionDialog] = useState(false);
  const [showEditTransactionDialog, setShowEditTransactionDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isValidatingFilters, setIsValidatingFilters] = useState(false);

  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const validatedFilters = useMemo(() => {
    try {
      setIsValidatingFilters(true);
      const isStartDateValid = !dateFilter.startDate || /^\d{4}-\d{2}-\d{2}$/.test(dateFilter.startDate);
      const isEndDateValid = !dateFilter.endDate || /^\d{4}-\d{2}-\d{2}$/.test(dateFilter.endDate);
      const isTypeValid = !typeFilter || ['entrada', 'saida'].includes(typeFilter);
      const isPaymentMethodValid = !paymentMethodFilter || paymentMethodFilter.trim() !== '';
      let areDatesChronological = true;
      if (isStartDateValid && isEndDateValid && dateFilter.startDate && dateFilter.endDate) {
        areDatesChronological = new Date(dateFilter.startDate) <= new Date(dateFilter.endDate);
      }
      if (!isStartDateValid || !isEndDateValid) {
        setErrorMessage("Formato de data inválido");
        setIsValidatingFilters(false);
        return null;
      }
      if (!isTypeValid) {
        setErrorMessage("Tipo de transação inválido");
        setIsValidatingFilters(false);
        return null;
      }
      if (!isPaymentMethodValid) {
        setErrorMessage("Método de pagamento inválido");
        setIsValidatingFilters(false);
        return null;
      }
      if (!areDatesChronological) {
        setErrorMessage("A data inicial deve ser anterior à data final");
        setIsValidatingFilters(false);
        return null;
      }
      setErrorMessage(null);
      setIsValidatingFilters(false);
      return {
        startDate: dateFilter.startDate,
        endDate: dateFilter.endDate,
        type: typeFilter as 'entrada' | 'saida' | undefined,
        paymentMethod: paymentMethodFilter
      };
    } catch (error) {
      console.error("Erro ao validar filtros:", error);
      setErrorMessage("Erro ao processar filtros");
      setIsValidatingFilters(false);
      return null;
    }
  }, [dateFilter.startDate, dateFilter.endDate, typeFilter, paymentMethodFilter]);
  const {
    transactions,
    isLoading,
    isError,
    error,
    totals,
    paymentMethods,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    getUserProfile
  } = useTransactions(validatedFilters || undefined);
  // Resetar para a primeira página quando os filtros mudarem
  useEffect(() => {
    setCurrentPage(1);
  }, [dateFilter.startDate, dateFilter.endDate, typeFilter, paymentMethodFilter, searchTerm]);

  useEffect(() => {
    if (isError && error) {
      setErrorMessage(`Erro ao carregar dados: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      toast.error(`Erro ao carregar dados: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }, [isError, error]);
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const profile = await getUserProfile();
        setUserProfile(profile);
      } catch (error) {
        console.error("Erro ao buscar perfil do usuário:", error);
        toast.error("Não foi possível carregar o perfil do usuário");
      }
    };
    if (user) {
      fetchUserProfile();
    }
  }, [user, getUserProfile]);
  const form = useForm({
    defaultValues: {
      type: "entrada",
      description: "",
      amount: "",
      payment_method_id: "",
      transaction_date: new Date().toISOString().split('T')[0],
      category: "",
      comprovativo: ""
    }
  });
  const editForm = useForm({
    defaultValues: {
      type: "entrada",
      description: "",
      amount: "",
      payment_method_id: "",
      transaction_date: "",
      category: "",
      comprovativo: ""
    }
  });
  /**
   * Funções para controle de paginação
   */

  /**
   * Manipula a mudança de página na paginação
   *
   * @param {number} page - Número da página selecionada
   */
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  /**
   * Manipula a mudança no tamanho da página
   *
   * @param {number} newPageSize - Novo tamanho de página selecionado
   */
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Resetar para a primeira página ao mudar o tamanho da página
  };

  // Filtrar transações com base no termo de busca
  const filteredTransactions = useMemo(() => {
    if (!transactions) return [];

    return transactions.filter(transaction => {
      const searchLower = searchTerm.toLowerCase();
      return (
        transaction.description?.toLowerCase().includes(searchLower) ||
        transaction.payment_method?.name?.toLowerCase().includes(searchLower) ||
        transaction.category?.toLowerCase().includes(searchLower) ||
        (transaction.amount?.toString() || '').includes(searchTerm)
      );
    });
  }, [transactions, searchTerm]);

  // Aplicar paginação aos dados filtrados
  const { paginatedData, totalItems, totalPages } = useMemo(() => {
    return paginateData(
      filteredTransactions,
      currentPage,
      pageSize
    );
  }, [filteredTransactions, currentPage, pageSize]);

  const resetFilters = () => {
    try {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      setDateFilter({
        startDate: firstDay.toISOString().split('T')[0],
        endDate: lastDay.toISOString().split('T')[0]
      });
      setTypeFilter(null);
      setPaymentMethodFilter(null);
      setSearchTerm("");
      setErrorMessage(null);
    } catch (error) {
      console.error("Erro ao resetar filtros:", error);
      toast.error("Não foi possível resetar os filtros");
    }
  };
  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    try {
      setDateFilter(prev => ({
        ...prev,
        [field]: value
      }));
    } catch (error) {
      console.error(`Erro ao definir ${field}:`, error);
      toast.error(`Erro ao definir data: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };
  const handleTypeFilterChange = (value: string | null) => {
    try {
      setTypeFilter(value);
    } catch (error) {
      console.error("Erro ao definir filtro de tipo:", error);
      toast.error(`Erro ao definir filtro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };
  const handlePaymentMethodFilterChange = (value: string | null) => {
    try {
      setPaymentMethodFilter(value);
    } catch (error) {
      console.error("Erro ao definir método de pagamento:", error);
      toast.error(`Erro ao definir método de pagamento: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };
  const handleNewTransaction = () => {
    try {
      form.reset({
        type: "entrada",
        description: "",
        amount: "",
        payment_method_id: "",
        transaction_date: new Date().toISOString().split('T')[0],
        category: "",
        comprovativo: ""
      });
      setShowNewTransactionDialog(true);
    } catch (error) {
      console.error("Erro ao abrir formulário de nova transação:", error);
      toast.error("Não foi possível abrir o formulário");
    }
  };
  const handleEditTransaction = (transaction: Transaction) => {
    try {
      if (!transaction) {
        throw new Error("Transação inválida");
      }
      setSelectedTransaction(transaction);
      const safeTransaction = {
        type: transaction.type || "entrada",
        description: transaction.description || "",
        amount: transaction.amount ? transaction.amount.toString() : "",
        payment_method_id: transaction.payment_method_id || "",
        transaction_date: transaction.transaction_date ? new Date(transaction.transaction_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        category: transaction.category || ""
      };
      editForm.reset(safeTransaction);
      setShowEditTransactionDialog(true);
    } catch (error) {
      console.error("Erro ao abrir formulário de edição:", error);
      toast.error("Não foi possível editar a transação");
    }
  };
  const handleDeleteTransaction = (transaction: Transaction) => {
    try {
      if (!transaction) {
        throw new Error("Transação inválida");
      }
      setSelectedTransaction(transaction);
      setShowDeleteDialog(true);
    } catch (error) {
      console.error("Erro ao iniciar exclusão:", error);
      toast.error("Não foi possível iniciar a exclusão da transação");
    }
  };
  const onSubmitNewTransaction = (data: any) => {
    try {
      if (!user?.id) {
        toast.error("Usuário não autenticado");
        return;
      }
      if (!userProfile) {
        toast.error("Perfil de usuário não encontrado");
        return;
      }
      const amount = parseFloat(data.amount);
      if (isNaN(amount) || amount <= 0) {
        toast.error("Valor inválido");
        return;
      }
      if (!data.description.trim()) {
        toast.error("Descrição é obrigatória");
        return;
      }
      if (!data.payment_method_id) {
        toast.error("Método de pagamento é obrigatório");
        return;
      }
      addTransaction({
        type: data.type,
        description: data.description,
        amount: amount,
        payment_method_id: data.payment_method_id,
        transaction_date: new Date(data.transaction_date).toISOString(),
        category: data.category || null,
        user_id: userProfile.id
      });
      setShowNewTransactionDialog(false);
    } catch (error) {
      console.error("Erro ao adicionar transação:", error);
      toast.error("Não foi possível adicionar a transação");
    }
  };
  const onSubmitEditTransaction = (data: any) => {
    try {
      if (!selectedTransaction) {
        toast.error("Nenhuma transação selecionada");
        return;
      }
      const amount = parseFloat(data.amount);
      if (isNaN(amount) || amount <= 0) {
        toast.error("Valor inválido");
        return;
      }
      if (!data.description.trim()) {
        toast.error("Descrição é obrigatória");
        return;
      }
      updateTransaction({
        id: selectedTransaction.id,
        updates: {
          type: data.type,
          description: data.description,
          amount: amount,
          payment_method_id: data.payment_method_id,
          transaction_date: new Date(data.transaction_date).toISOString(),
          category: data.category || null
        }
      });
      setShowEditTransactionDialog(false);
    } catch (error) {
      console.error("Erro ao atualizar transação:", error);
      toast.error("Não foi possível atualizar a transação");
    }
  };
  const confirmDelete = () => {
    try {
      if (!selectedTransaction) {
        toast.error("Nenhuma transação selecionada");
        return;
      }
      deleteTransaction(selectedTransaction.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Erro ao excluir transação:", error);
      toast.error("Não foi possível excluir a transação");
    }
  };
  const handleMonthChange = (change: number) => {
    try {
      const currentStartDate = dateFilter.startDate ? new Date(dateFilter.startDate) : new Date();
      if (isNaN(currentStartDate.getTime())) {
        throw new Error("Data inicial inválida");
      }
      const newStartDate = new Date(currentStartDate);
      newStartDate.setDate(1);
      newStartDate.setMonth(newStartDate.getMonth() + change);
      const newEndDate = new Date(newStartDate);
      newEndDate.setMonth(newEndDate.getMonth() + 1);
      newEndDate.setDate(0);
      console.log("Moving from", currentStartDate, "to", newStartDate);
      console.log("New period:", newStartDate, "to", newEndDate);
      setDateFilter({
        startDate: newStartDate.toISOString().split('T')[0],
        endDate: newEndDate.toISOString().split('T')[0]
      });
    } catch (error) {
      console.error("Erro ao mudar mês:", error);
      toast.error("Não foi possível alterar o período");
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      setDateFilter({
        startDate: firstDay.toISOString().split('T')[0],
        endDate: lastDay.toISOString().split('T')[0]
      });
    }
  };
  const currentMonth = React.useMemo(() => {
    try {
      return dateFilter.startDate ? new Date(dateFilter.startDate).toLocaleDateString('pt-BR', {
        month: 'long',
        year: 'numeric'
      }) : '';
    } catch (error) {
      console.error("Erro ao formatar mês:", error);
      return 'Período atual';
    }
  }, [dateFilter.startDate]);
  const paymentMethodStats = React.useMemo(() => {
    try {
      const stats: Array<{
        name: string;
        amount: number;
        percentage: number;
      }> = [];
      const methodDistribution = totals?.paymentMethodDistribution || {};
      for (const [name, data] of Object.entries(methodDistribution)) {
        if (!name) continue;
        if (typeof data === 'object' && data !== null && 'amount' in data && 'percentage' in data) {
          stats.push({
            name,
            amount: data.amount || 0,
            percentage: data.percentage || 0
          });
        } else {
          const amountValue = typeof data === 'number' ? data : 0;
          stats.push({
            name,
            amount: amountValue,
            percentage: 0
          });
        }
      }
      return stats.sort((a, b) => b.percentage - a.percentage);
    } catch (error) {
      console.error("Erro ao calcular estatísticas de métodos de pagamento:", error);
      return [];
    }
  }, [totals?.paymentMethodDistribution]);
  const NoDataFallback = () => <div className="flex flex-col items-center justify-center p-10 text-gray-500">
      <Wallet className="h-16 w-16 mb-4 opacity-30" />
      <h3 className="text-lg font-medium">Nenhuma transação encontrada</h3>
      <p className="text-sm mt-2 text-center">Tente ajustar os filtros ou adicione uma nova transação</p>
      <Button variant="outline" className="mt-4" onClick={resetFilters}>
        Resetar filtros
      </Button>
    </div>;
  if (isValidatingFilters) {
    return <div className="p-6 max-w-7xl mx-auto">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl font-bold">Fluxo de Caixa</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">Gestão financeira do sistema</p>
          </div>
        </div>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2">Validando filtros...</span>
        </div>
      </div>;
  }
  if (errorMessage) {
    return <div className="p-6 max-w-7xl mx-auto">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl font-bold">Fluxo de Caixa</h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">Gestão financeira do sistema</p>
          </div>
        </div>

        <Alert variant="destructive" className="mb-6">
          <AlertDescription>
            {errorMessage}
          </AlertDescription>
        </Alert>

        <Button onClick={resetFilters} variant="outline">
          Resetar filtros
        </Button>
      </div>;
  }
  return <div className="p-6 max-w-7xl mx-auto">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-2xl font-bold">Fluxo de Caixa</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Gestão financeira do sistema </p>
        </div>

        <div className="flex flex-wrap gap-3">
          <div className="flex items-center">
            <Button size="sm" variant="outline" className="px-2" onClick={() => handleMonthChange(-1)}>
              &lt;
            </Button>

            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="outline" className="gap-1 mx-1 min-w-32">
                  <Calendar className="h-4 w-4" />
                  {currentMonth || 'Selecione um período'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-2 w-auto mx-[10px]">
                <div className="grid gap-2">
                  <div className="grid grid-cols-2 gap-2">
                    <FormItem className="flex flex-col">
                      <FormLabel>De</FormLabel>
                      <Input type="date" value={dateFilter.startDate || ''} onChange={e => handleDateChange('startDate', e.target.value)} />
                    </FormItem>
                    <FormItem className="flex flex-col">
                      <FormLabel>Até</FormLabel>
                      <Input type="date" value={dateFilter.endDate || ''} onChange={e => handleDateChange('endDate', e.target.value)} />
                    </FormItem>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Button size="sm" variant="outline" className="px-2" onClick={() => handleMonthChange(1)}>
              &gt;
            </Button>
          </div>

          <Popover>
            <PopoverTrigger asChild>
              <Button size="sm" variant="outline" className="gap-1">
                <Filter className="h-4 w-4" />
                Filtros
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 w-72 mx-[35px]">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Tipo</h4>
                  <div className="flex gap-2">
                    <Button variant={typeFilter === null ? "default" : "outline"} size="sm" onClick={() => handleTypeFilterChange(null)} className="text-xs flex-1">
                      Todos
                    </Button>
                    <Button variant={typeFilter === "entrada" ? "default" : "outline"} size="sm" onClick={() => handleTypeFilterChange("entrada")} className="text-xs flex-1">
                      Entradas
                    </Button>
                    <Button variant={typeFilter === "saida" ? "default" : "outline"} size="sm" onClick={() => handleTypeFilterChange("saida")} className="text-xs flex-1">
                      Saídas
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Método de Pagamento</h4>
                  <Select value={paymentMethodFilter || ""} onValueChange={value => handlePaymentMethodFilterChange(value || null)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos os métodos" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="_all">Todos os métodos</SelectItem>
                      {(paymentMethods || []).map(method => <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>)}
                    </SelectContent>
                  </Select>
                </div>

                <Button size="sm" variant="outline" onClick={resetFilters}>
                  Limpar filtros
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <Button size="sm" className="gap-1" onClick={handleNewTransaction}>
            <Plus className="h-4 w-4" />
            Nova Transação
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <DashboardCard title="Total Entradas" value={formatCurrency(totals?.totalIncome || 0)} icon={<ArrowUp className="h-6 w-6 text-green-600 dark:text-green-400" />} className="border-l-4 border-green-500" />

        <DashboardCard title="Total Saídas" value={formatCurrency(totals?.totalExpense || 0)} icon={<ArrowDown className="h-6 w-6 text-red-600 dark:text-red-400" />} className="border-l-4 border-red-500" />

        <DashboardCard title="Saldo Total" value={formatCurrency(totals?.balance || 0)} icon={<Wallet className="h-6 w-6" />} className="border-l-4 border-oluchys-accent" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {isLoading ? <>
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6 lg:col-span-2 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          </> : <>
            {Array.isArray(totals?.chartData) && totals.chartData.length > 0 ? <DashboardChart title="Fluxo de Caixa" subtitle="Movimentação financeira por dia" data={totals.chartData} className="lg:col-span-2" /> : <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6 lg:col-span-2">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold">Fluxo de Caixa</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Movimentação financeira por dia
                  </p>
                </div>
                <NoDataFallback />
              </div>}

            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 p-6">
              <div className="mb-6">
                <h3 className="text-lg font-semibold">Métodos de Pagamento</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Distribuição por forma de pagamento
                </p>
              </div>

              <div className="space-y-4">
                {paymentMethodStats && paymentMethodStats.length > 0 ? paymentMethodStats.map((method, index) => <div key={method.name} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${index === 0 ? "bg-blue-100 dark:bg-blue-900/30" : index === 1 ? "bg-green-100 dark:bg-green-900/30" : "bg-purple-100 dark:bg-purple-900/30"}`}>
                          <CreditCard className={`h-4 w-4 ${index === 0 ? "text-blue-600 dark:text-blue-400" : index === 1 ? "text-green-600 dark:text-green-400" : "text-purple-600 dark:text-purple-400"}`} />
                        </div>
                        <span className="font-medium">{method.name}</span>
                      </div>
                      <span className="text-sm font-medium">{method.percentage}%</span>
                    </div>) : <div className="flex items-center justify-center p-6 text-gray-500">
                    Nenhum dado disponível
                  </div>}
              </div>
            </div>
          </>}
      </div>

      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-card border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800">
          <h3 className="text-lg font-semibold mb-4">Transações</h3>

          {/* Linha com seletor de registros e campo de pesquisa */}
          <div className="flex flex-col-reverse sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Seletor de registros por página */}
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <span className="text-sm text-gray-500 dark:text-gray-400">Mostrar</span>
              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="30">30</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
              <span className="text-sm text-gray-500 dark:text-gray-400">registros por página</span>
            </div>

            {/* Campo de pesquisa */}
            <div className="relative w-full sm:w-auto mb-4 sm:mb-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Buscar transação..."
                className="pl-9 h-8 w-full sm:w-[220px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </div>

        {isLoading ? <div className="flex items-center justify-center p-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div> : <div className="overflow-x-auto">
            {filteredTransactions.length > 0 ? <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-800 text-left">
                    <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Descrição</th>
                    <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Data</th>
                    <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400">Método</th>
                    <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400 text-right">Valor</th>
                    <th className="px-6 py-3 font-medium text-gray-500 dark:text-gray-400 text-right">Ações</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 dark:divide-gray-800">
                  {paginatedData.map(transaction => <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 ${transaction.type === "entrada" ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" : "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"}`}>
                            {transaction.type === "entrada" ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
                          </div>
                          <span className="font-medium">{transaction.description}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
                        {transaction.transaction_date ? new Date(transaction.transaction_date).toLocaleDateString('pt-BR') : '-'}
                      </td>
                      <td className="px-6 py-4 text-gray-500 dark:text-gray-400">
                        {transaction.payment_method?.name || '-'}
                      </td>
                      <td className={`px-6 py-4 text-right font-medium ${transaction.type === "entrada" ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
                        {transaction.type === "entrada" ? "+" : "-"}
                        {formatCurrency(transaction.amount || 0)}
                      </td>
                      <td className="px-6 py-4 text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleEditTransaction(transaction)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20" onClick={() => handleDeleteTransaction(transaction)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>)}
                </tbody>
              </table> : <NoDataFallback />}

              {/* Componente de paginação */}
              {!isLoading && filteredTransactions.length > 0 && (
                <DataTablePagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={totalItems}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                />
              )}
          </div>}
      </div>

      <Dialog open={showNewTransactionDialog} onOpenChange={setShowNewTransactionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nova Transação</DialogTitle>
            <DialogDescription>
              Adicione uma nova transação ao sistema.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={form.handleSubmit(onSubmitNewTransaction)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <FormItem>
                  <FormLabel required>Tipo</FormLabel>
                  <Select value={form.watch("type")} onValueChange={value => form.setValue("type", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entrada">Entrada</SelectItem>
                      <SelectItem value="saida">Saída</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>

                <FormItem>
                  <FormLabel required>Data</FormLabel>
                  <Input type="date" value={form.watch("transaction_date")} onChange={e => form.setValue("transaction_date", e.target.value)} />
                </FormItem>
              </div>

              <FormItem>
                <FormLabel required>Descrição</FormLabel>
                <Input placeholder="Descrição da transação" value={form.watch("description")} onChange={e => form.setValue("description", e.target.value)} />
              </FormItem>

              <div className="grid grid-cols-2 gap-4">
                <FormItem>
                  <FormLabel required>Valor</FormLabel>
                  <Input type="number" placeholder="0,00" min="0" step="0.01" value={form.watch("amount")} onChange={e => form.setValue("amount", e.target.value)} />
                </FormItem>

                <FormItem>
                  <FormLabel required>Método de Pagamento</FormLabel>
                  <Select value={form.watch("payment_method_id")} onValueChange={value => form.setValue("payment_method_id", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                    <SelectContent>
                      {(paymentMethods || []).map(method => <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>)}
                    </SelectContent>
                  </Select>
                </FormItem>
              </div>

              <FormItem>
                <FormLabel>Categoria (opcional)</FormLabel>
                <Input placeholder="Categoria" value={form.watch("category")} onChange={e => form.setValue("category", e.target.value)} />
              </FormItem>
            </div>

            <div className="text-xs text-gray-500 mb-4">
              <span className="text-destructive">*</span> Campos obrigatórios
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowNewTransactionDialog(false)}>
                Cancelar
              </Button>
              <Button type="submit" className="my-0">
                Adicionar
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={showEditTransactionDialog} onOpenChange={setShowEditTransactionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Transação</DialogTitle>
            <DialogDescription>
              Atualize os dados da transação selecionada.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={editForm.handleSubmit(onSubmitEditTransaction)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <FormItem>
                  <FormLabel required>Tipo</FormLabel>
                  <Select value={editForm.watch("type")} onValueChange={value => editForm.setValue("type", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entrada">Entrada</SelectItem>
                      <SelectItem value="saida">Saída</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>

                <FormItem>
                  <FormLabel required>Data</FormLabel>
                  <Input type="date" value={editForm.watch("transaction_date")} onChange={e => editForm.setValue("transaction_date", e.target.value)} />
                </FormItem>
              </div>

              <FormItem>
                <FormLabel required>Descrição</FormLabel>
                <Input placeholder="Descrição da transação" value={editForm.watch("description")} onChange={e => editForm.setValue("description", e.target.value)} />
              </FormItem>

              <div className="grid grid-cols-2 gap-4">
                <FormItem>
                  <FormLabel required>Valor</FormLabel>
                  <Input type="number" placeholder="0,00" min="0" step="0.01" value={editForm.watch("amount")} onChange={e => editForm.setValue("amount", e.target.value)} />
                </FormItem>

                <FormItem>
                  <FormLabel required>Método de Pagamento</FormLabel>
                  <Select value={editForm.watch("payment_method_id")} onValueChange={value => editForm.setValue("payment_method_id", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                    <SelectContent>
                      {(paymentMethods || []).map(method => <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>)}
                    </SelectContent>
                  </Select>
                </FormItem>
              </div>

              <FormItem>
                <FormLabel>Categoria (opcional)</FormLabel>
                <Input placeholder="Categoria" value={editForm.watch("category")} onChange={e => editForm.setValue("category", e.target.value)} />
              </FormItem>
            </div>

            <div className="text-xs text-gray-500 mb-4">
              <span className="text-destructive">*</span> Campos obrigatórios
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditTransactionDialog(false)}>
                Cancelar
              </Button>
              <Button type="submit">
                Salvar
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta transação? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteDialog(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-500 hover:bg-red-600">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>;
};
export default CashFlow;