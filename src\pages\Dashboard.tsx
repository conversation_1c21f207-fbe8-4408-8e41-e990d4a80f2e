/**
 * @file Dashboard.tsx
 * @description Página principal do dashboard que exibe uma visão geral do sistema,
 * incluindo métricas de vendas, estoque, gráficos e alertas. Também permite
 * a criação rápida de novas vendas diretamente do dashboard.
 */

import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import NewSaleForm from "@/components/NewSaleForm";
import { useSales } from "@/hooks/use-sales";
import { useDashboard } from "@/hooks/use-dashboard";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import DashboardCards from "@/components/dashboard/DashboardCards";
import DashboardCharts from "@/components/dashboard/DashboardCharts";

/**
 * Página principal do Dashboard
 *
 * @component
 * @returns {JSX.Element} Página do Dashboard renderizada
 */
const Dashboard = () => {
  const navigate = useNavigate();
  // Estado para controlar a abertura/fechamento do diálogo de nova venda
  const [openNewSaleDialog, setOpenNewSaleDialog] = useState(false);

  // Obtém dados e funções relacionadas a vendas do hook personalizado
  const {
    productVariants, // Variações de produtos disponíveis
    statuses, // Status possíveis para vendas
    paymentMethods, // Métodos de pagamento disponíveis
    sellers, // Lista de vendedores
    deliveryPersons, // Lista de entregadores
    addSale // Função para adicionar uma nova venda
  } = useSales();

  // Obtém dados do dashboard do hook personalizado
  const {
    data: dashboardData, // Dados principais do dashboard
    isLoading, // Estado de carregamento
    isError // Estado de erro
  } = useDashboard();

  /**
   * Abre o diálogo para criar uma nova venda
   */
  const handleNewSale = () => {
    setOpenNewSaleDialog(true);
  };

  /**
   * Exibe um toast com o número de notificações não lidas
   */
  const handleNotificationsClick = () => {
    toast("Notificações", {
      description: `Você tem ${dashboardData?.notifications.length || 0} notificações não lidas.`
    });
  };

  /**
   * Navega para a página de inventário
   * Usado quando o usuário clica para ver produtos com estoque baixo
   */
  const handleViewInventory = () => {
    navigate('/inventory');
  };

  /**
   * Processa o envio do formulário de nova venda
   *
   * @param {any} data - Dados da venda do formulário
   * @param {File} [proofFile] - Arquivo de comprovante de pagamento opcional
   */
  const handleNewSaleSubmit = (data: any, proofFile?: File) => {
    addSale({ sale: data, proofFile });
    setOpenNewSaleDialog(false);
  };

  if (isError) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
          <h3 className="text-lg font-medium">Erro ao carregar dados</h3>
          <p>Ocorreu um erro ao carregar os dados do dashboard. Por favor, tente novamente mais tarde.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <DashboardHeader
        onNewSaleClick={handleNewSale}
      />

      <DashboardCards
        isLoading={isLoading}
        dashboardData={dashboardData}
      />

      <DashboardCharts
        isLoading={isLoading}
        dashboardData={dashboardData}
        onViewInventory={handleViewInventory}
      />

      {/* New Sale Dialog */}
      <Dialog open={openNewSaleDialog} onOpenChange={setOpenNewSaleDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Nova Venda</DialogTitle>
            <DialogDescription>
              Preencha os detalhes para registrar uma nova venda.
            </DialogDescription>
          </DialogHeader>
          <NewSaleForm
            onSubmit={handleNewSaleSubmit}
            onCancel={() => setOpenNewSaleDialog(false)}
            productVariants={productVariants}
            paymentMethods={paymentMethods}
            statuses={statuses}
            sellers={sellers}
            deliveryPersons={deliveryPersons}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Dashboard;
