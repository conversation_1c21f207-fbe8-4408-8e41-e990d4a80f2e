/**
 * @file Inventory.tsx
 * @description Página de gerenciamento de estoque que permite visualizar, adicionar e editar produtos.
 * Exibe métricas de estoque, como total de produtos, categorias, itens com estoque baixo e valor total.
 * Permite filtrar, ordenar e paginar a lista de produtos.
 */

import React, { useState } from "react";
import { Package, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import InventoryTable from "@/components/InventoryTable";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import NewProductForm from "@/components/NewProductForm";
import {
  fetchProductVariants,
  fetchLowStockProducts,
  addProduct,
  addProductVariant
} from "@/services/productService";
import { useQuery } from "@tanstack/react-query";
import { fetchCategories } from "@/services/categoryService";
import { fetchColors } from "@/services/colorService";
import { fetchSizes } from "@/services/sizeService";
import { useAuth } from "@/context/AuthContext";

/**
 * Página de gerenciamento de estoque
 *
 * @component
 * @returns {JSX.Element} Página de inventário renderizada
 */
const Inventory = () => {
  // Estado para controlar a abertura/fechamento do diálogo de novo produto
  const [openNewProductDialog, setOpenNewProductDialog] = useState(false);

  // Obtém informações do usuário autenticado
  const { profile } = useAuth();

  // Determina as permissões do usuário com base em seu papel (role)
  const isAdmin = profile?.role === 'admin';
  const canAddProducts = isAdmin;
  const canEditProducts = isAdmin;
  const canDeleteProducts = isAdmin;

  /**
   * Consultas para buscar dados de categorias, cores e tamanhos
   * Estes dados são necessários para exibir e filtrar produtos
   */
  // Buscar categorias, cores e tamanhos primeiro
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['categories'],
    queryFn: fetchCategories,
  });

  const { data: colors = [], isLoading: isLoadingColors } = useQuery({
    queryKey: ['colors'],
    queryFn: fetchColors,
  });

  const { data: sizes = [], isLoading: isLoadingSizes } = useQuery({
    queryKey: ['sizes'],
    queryFn: fetchSizes,
  });

  /**
   * Consulta para buscar variações de produtos
   * Depende do carregamento prévio das categorias
   */
  const { data: productVariants = [], isLoading: isLoadingProducts, refetch: refetchProducts } = useQuery({
    queryKey: ['productVariants', categories.length],
    queryFn: fetchProductVariants,
    enabled: !isLoadingCategories, // Só busca produtos quando as categorias estiverem carregadas
  });

  /**
   * Consulta para buscar produtos com estoque baixo
   * Usada para exibir alertas e contagem de itens críticos
   */
  const { data: lowStockProducts = [] } = useQuery({
    queryKey: ['lowStockProducts', categories.length],
    queryFn: fetchLowStockProducts,
    enabled: !isLoadingCategories, // Só busca produtos com estoque baixo quando as categorias estiverem carregadas
  });

  /**
   * Cálculo de métricas para os cards de resumo
   */
  // Calcular totais para os cards
  const totalVariants = productVariants.length;
  const totalCategories = categories.length;
  const lowStockCount = lowStockProducts.length;

  /**
   * Calcula o valor total do estoque multiplicando o preço de cada produto pela quantidade
   */
  const totalStockValue = productVariants.reduce((total, variant) => {
    if (variant.product?.price) {
      return total + (variant.product.price * variant.quantity);
    }
    return total;
  }, 0);

  /**
   * Formata um valor numérico para o formato de moeda angolana (Kwanza)
   *
   * @param {number} value - Valor a ser formatado
   * @returns {string} Valor formatado como moeda
   */
  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-AO', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }) + ' Kz';
  };

  /**
   * Manipula a criação de um novo produto e sua variação
   * Processo em duas etapas: primeiro cria o produto base, depois a variação específica
   *
   * @param {any} data - Dados do formulário de novo produto
   */
  const handleNewProduct = async (data: any) => {
    try {
      // Primeiro, criar o produto base com nome, categoria e preço
      const newProduct = await addProduct({
        name: data.name,
        category_id: data.category_id,
        price: parseFloat(data.price)
      });

      // Depois, criar a variação do produto com cor, tamanho e quantidade
      await addProductVariant({
        product_id: newProduct.id,
        color_id: data.color_id,
        size_id: data.size_id,
        quantity: parseInt(data.quantity)
      });

      // Atualizar a lista de produtos e fechar o diálogo
      refetchProducts();
      setOpenNewProductDialog(false);
      toast.success("Produto adicionado com sucesso");
    } catch (error) {
      console.error("Erro ao adicionar produto:", error);
      toast.error("Erro ao adicionar produto");
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-2xl font-bold">Estoque</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Gerenciamento de produtos e estoque
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-4">
            <Package className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Total de Produtos</p>
            <h3 className="text-1.25rem font-semibold">{totalVariants}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-4">
            <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Categorias</p>
            <h3 className="text-1.25rem font-semibold">{totalCategories}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center mr-4">
            <Package className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Estoque Baixo</p>
            <h3 className="text-1.25rem font-semibold">{lowStockCount}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mr-4">
            <Package className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Valor Total</p>
            <h3 className="text-1.25rem font-semibold">{formatCurrency(totalStockValue)}</h3>
          </div>
        </div>
      </div>

      <InventoryTable
        isLoading={isLoadingProducts}
        productVariants={productVariants}
        refetchProducts={refetchProducts}
        onAddProductClick={() => setOpenNewProductDialog(true)}
        categories={categories}
        colors={colors}
        sizes={sizes}
        canAddProducts={canAddProducts}
        canEditProducts={canEditProducts}
        canDeleteProducts={canDeleteProducts}
      />

      <Dialog open={openNewProductDialog} onOpenChange={setOpenNewProductDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Novo Produto</DialogTitle>
            <DialogDescription>
              Preencha os detalhes para adicionar um novo produto ao estoque.
            </DialogDescription>
          </DialogHeader>
          <NewProductForm
            onSubmit={handleNewProduct}
            onCancel={() => setOpenNewProductDialog(false)}
            categories={categories}
            colors={colors}
            sizes={sizes}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Inventory;
