/**
 * @file Login.tsx
 * @description Página de login que permite aos usuários autenticarem-se no sistema.
 * Fornece um formulário com campos para email e senha, com validação básica e
 * feedback visual durante o processo de autenticação.
 */

import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LockIcon, MailIcon, Eye, EyeOff, InfoIcon } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

/**
 * Página de login do sistema
 *
 * @component
 * @returns {JSX.Element} Página de login renderizada
 */
const Login = () => {
  // Estados para controlar os campos do formulário e o estado de carregamento
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false); // Controla a visibilidade da senha

  // Hooks para autenticação e navegação
  const { signIn } = useAuth();
  const navigate = useNavigate();

  /**
   * Manipula o envio do formulário de login
   * Tenta autenticar o usuário e redireciona para a página inicial em caso de sucesso
   *
   * @param {React.FormEvent} e - Evento de formulário
   */
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const success = await signIn(email, password);
      if (success) {
        navigate("/"); // Redireciona para a página inicial após login bem-sucedido
      }
    } catch (error) {
      console.error("Erro ao fazer login:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card className="w-full">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">SalesFlow Demo</CardTitle>
            <CardDescription>
              Sistema de Gestão de Vendas - Versão Demonstração
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Credenciais de teste */}
            <Alert className="mb-4 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Credenciais de Teste</AlertTitle>
              <AlertDescription className="mt-2 space-y-1">
                <div><strong>Administrador:</strong> <EMAIL> | Senha: 123</div>
                <div><strong>Vendedor:</strong> <EMAIL> | Senha: 123</div>
              </AlertDescription>
            </Alert>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <MailIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Senha</Label>
                </div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <LockIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                  </Button>
                </div>
              </div>
              <Button type="submit" className="w-full bg-oluchys-accent hover:bg-oluchys-highlight text-white" disabled={loading}>
                {loading ? "Entrando..." : "Entrar"}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col items-center space-y-2">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              © {new Date().getFullYear()} SalesFlow Demo. Versão para Portfolio.
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 text-center">
              Esta é uma versão de demonstração. Todos os dados são fictícios e armazenados localmente.
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
