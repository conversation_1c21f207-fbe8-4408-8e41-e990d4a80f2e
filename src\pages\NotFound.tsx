/**
 * @file NotFound.tsx
 * @description Página de erro 404 exibida quando o usuário tenta acessar uma rota
 * que não existe. Registra a tentativa de acesso no console para fins de depuração.
 */

import { useLocation } from "react-router-dom";
import { useEffect } from "react";

/**
 * Página de erro 404 (Not Found)
 *
 * @component
 * @returns {JSX.Element} Página de erro 404 renderizada
 */
const NotFound = () => {
  // Obtém informações sobre a localização atual (rota)
  const location = useLocation();

  /**
   * Efeito para registrar no console quando um usuário tenta acessar uma rota inexistente
   * Facilita a identificação de problemas de navegação ou links quebrados
   */
  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  /**
   * Renderiza uma interface amigável para o erro 404
   * Inclui um título, mensagem de erro e link para retornar à página inicial
   */
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4 dark:text-white">404</h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">Oops! Página não encontrada</p>
        <a href="/" className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline">
          Voltar para a Página Inicial
        </a>
      </div>
    </div>
  );
};

export default NotFound;
