/**
 * @file Reports.tsx
 * @description Página de relatórios que permite aos administradores visualizar
 * e exportar dados de vendas, entregas, produtos e métodos de pagamento.
 * Inclui filtros por período, vendedor e status, e permite exportar os dados para PDF.
 */

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  Package,
  Banknote,
  Users,
  TrendingUp,
  ShoppingBag,
  Truck,
  CreditCard
} from "lucide-react";
import { MetricCard } from "@/components/reports/MetricCard";
import PaymentPieChart from "@/components/reports/PaymentPieChart";
import SparklineChart from "@/components/reports/SparklineChart";
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useReports, FilterPeriod } from "@/hooks/use-reports";

/**
 * Página de relatórios que permite visualizar e exportar dados de vendas
 *
 * @component
 * @returns {JSX.Element} Página de relatórios com gráficos, tabelas e opções de exportação
 */
const Reports = () => {
  const navigate = useNavigate();
  const { profile } = useAuth();
  // Controla a aba ativa nos relatórios
  const [, setActiveTab] = useState("sellers");

  /**
   * Efeito para verificar se o usuário tem permissões de administrador
   * Redireciona para a página inicial caso não tenha acesso
   */
  useEffect(() => {
    const checkAdmin = async () => {
      if (!profile) return;

      if (profile.role !== 'admin') {
        toast.error("Acesso restrito", {
          description: "Você não tem permissão para acessar esta página."
        });
        navigate("/");
      }
    };

    checkAdmin();
  }, [profile, navigate]);

  /**
   * Formata um valor numérico para o formato de moeda angolana (Kwanza)
   *
   * @param {number} value - Valor a ser formatado
   * @returns {string} Valor formatado como moeda (ex: "10 000 Kz")
   */
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Referência para os gráficos que serão exportados para PDF
  // Implementação futura para capturar gráficos e adicionar ao PDF

  /**
   * Gera um arquivo PDF com os dados dos relatórios
   * Inclui cabeçalho elaborado, gráficos, tabelas de dados e numeração de páginas
   */
  const generatePDF = () => {
    try {
      // Criar documento PDF
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      const margin = 14;

      // Função para adicionar cabeçalho em cada página
      const addHeader = (pageNumber: number) => {
        // Adicionar retângulo colorido no topo
        doc.setFillColor(31, 64, 176); // Azul escuro (#1f40b0)
        doc.rect(0, 0, pageWidth, 25, 'F');

        // Adicionar título do relatório
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(16);
        doc.setFont(undefined, 'bold');
        doc.text("RELATÓRIO OLUCHYS", margin, 15);

        // Adicionar informações da empresa
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8);
        doc.setFont(undefined, 'normal');
        doc.text("Gestão de Estoque e Vendas", pageWidth - margin, 10, { align: 'right' });
        doc.text("Versão 1.0.0", pageWidth - margin, 15, { align: 'right' });
        doc.text(`Gerado em: ${format(new Date(), "dd/MM/yyyy HH:mm", { locale: pt })}`
          , pageWidth - margin, 20, { align: 'right' });

        // Adicionar linha separadora
        doc.setDrawColor(230, 230, 230);
        doc.line(margin, 30, pageWidth - margin, 30);

        // Adicionar período do relatório
        doc.setTextColor(0, 0, 0);
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text(`Período: ${formattedDateRange()}`, margin, 38);
      };

      // Função para adicionar rodapé em cada página
      const addFooter = (pageNumber: number, totalPages: number) => {
        // Adicionar linha separadora
        doc.setDrawColor(230, 230, 230);
        doc.line(margin, pageHeight - 20, pageWidth - margin, pageHeight - 20);

        // Adicionar informações de rodapé
        doc.setTextColor(128, 128, 128);
        doc.setFontSize(8);
        doc.setFont(undefined, 'normal');
        doc.text("Desenvolvido por Carlos César", margin, pageHeight - 14);

        // Adicionar numeração de página
        doc.text(`Página ${pageNumber} de ${totalPages}`, pageWidth - margin, pageHeight - 14, { align: 'right' });
      };

      // Adicionar cabeçalho na primeira página
      addHeader(1);

      // Seção: Resumo do Período
      let yPos = 50;

      // Título da seção
      doc.setTextColor(30, 64, 175); // Azul escuro (#1E40AF)
      doc.setFontSize(14);
      doc.setFont(undefined, 'bold');
      doc.text("Resumo do Período", margin, yPos);

      // Adicionar linha colorida abaixo do título
      doc.setDrawColor(30, 64, 175);
      doc.setLineWidth(0.5);
      doc.line(margin, yPos + 2, margin + 50, yPos + 2);

      // Tabela de resumo
      doc.setTextColor(0, 0, 0);
      doc.setFont(undefined, 'normal');
      autoTable(doc, {
        startY: yPos + 8,
        head: [['Vendas Totais', 'Valor Total', 'Realizadas', 'Reservadas', 'Canceladas']],
        body: [[
          periodSummary.totalSales,
          formatCurrency(periodSummary.totalAmount),
          periodSummary.realizadas,
          periodSummary.reservadas,
          periodSummary.canceladas
        ]],
        headStyles: { fillColor: [30, 64, 175], textColor: [255, 255, 255] },
        alternateRowStyles: { fillColor: [245, 247, 250] },
        margin: { top: 10, right: margin, bottom: 10, left: margin },
        didDrawPage: function(data) {
          yPos = data.cursor.y + 5;
        }
      });

      // Seção: Vendas por Vendedor
      if (sellerReport.length > 0) {
        // Adicionar espaço antes do título da seção
        yPos += 10;

        // Título da seção
        doc.setTextColor(30, 64, 175);
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text("Vendas por Vendedor", margin, yPos);

        // Adicionar linha colorida abaixo do título
        doc.setDrawColor(30, 64, 175);
        doc.line(margin, yPos + 2, margin + 50, yPos + 2);

        // Tabela de vendedores
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        autoTable(doc, {
          startY: yPos + 8,
          head: [['Vendedor', 'Qtd. Vendas', 'Valor Total', 'Média por Venda']],
          body: sellerReport.map(seller => [
            seller.name,
            seller.totalSales,
            formatCurrency(seller.totalAmount),
            formatCurrency(seller.totalAmount / seller.totalSales)
          ]),
          headStyles: { fillColor: [30, 64, 175], textColor: [255, 255, 255] },
          alternateRowStyles: { fillColor: [245, 247, 250] },
          margin: { top: 10, right: margin, bottom: 10, left: margin },
          didDrawPage: function(data) {
            yPos = data.cursor.y + 5;
          }
        });
      }

      // Seção: Entregas por Entregador
      if (deliveryReport.length > 0) {
        // Adicionar espaço antes do título da seção
        yPos += 10;

        // Verificar se precisa adicionar uma nova página
        if (yPos > pageHeight - 80) {
          doc.addPage();
          addHeader(doc.internal.pages.length);
          yPos = 50;
        }

        // Título da seção
        doc.setTextColor(16, 185, 129); // Verde (#10B981)
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text("Entregas por Entregador", margin, yPos);

        // Adicionar linha colorida abaixo do título
        doc.setDrawColor(16, 185, 129);
        doc.line(margin, yPos + 2, margin + 50, yPos + 2);

        // Tabela de entregadores
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        autoTable(doc, {
          startY: yPos + 8,
          head: [['Entregador', 'Qtd. Entregas', 'Valor Total', 'Média por Entrega']],
          body: deliveryReport.map(delivery => [
            delivery.name,
            delivery.totalDeliveries,
            formatCurrency(delivery.totalAmount),
            formatCurrency(delivery.totalAmount / delivery.totalDeliveries)
          ]),
          headStyles: { fillColor: [16, 185, 129], textColor: [255, 255, 255] },
          alternateRowStyles: { fillColor: [245, 247, 250] },
          margin: { top: 10, right: margin, bottom: 10, left: margin },
          didDrawPage: function(data) {
            yPos = data.cursor.y + 5;
          }
        });
      }

      // Seção: Vendas por Método de Pagamento
      if (paymentReport.length > 0) {
        // Adicionar espaço antes do título da seção
        yPos += 10;

        // Verificar se precisa adicionar uma nova página
        if (yPos > pageHeight - 80) {
          doc.addPage();
          addHeader(doc.internal.pages.length);
          yPos = 50;
        }

        // Título da seção
        doc.setTextColor(139, 92, 246); // Roxo (#8B5CF6)
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text("Vendas por Método de Pagamento", margin, yPos);

        // Adicionar linha colorida abaixo do título
        doc.setDrawColor(139, 92, 246);
        doc.line(margin, yPos + 2, margin + 70, yPos + 2);

        // Tabela de métodos de pagamento
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        autoTable(doc, {
          startY: yPos + 8,
          head: [['Método', 'Qtd. Vendas', 'Valor Total', '% do Total']],
          body: paymentReport.map(payment => [
            payment.name,
            payment.totalSales,
            formatCurrency(payment.totalAmount),
            periodSummary.totalAmount > 0
              ? `${((payment.totalAmount / periodSummary.totalAmount) * 100).toFixed(1)}%`
              : '0%'
          ]),
          headStyles: { fillColor: [139, 92, 246], textColor: [255, 255, 255] },
          alternateRowStyles: { fillColor: [245, 247, 250] },
          margin: { top: 10, right: margin, bottom: 10, left: margin },
          didDrawPage: function(data) {
            yPos = data.cursor.y + 5;
          }
        });
      }

      // Seção: Produtos Mais Vendidos
      if (topProducts.length > 0) {
        // Adicionar espaço antes do título da seção
        yPos += 10;

        // Verificar se precisa adicionar uma nova página
        if (yPos > pageHeight - 80) {
          doc.addPage();
          addHeader(doc.internal.pages.length);
          yPos = 50;
        }

        // Título da seção
        doc.setTextColor(245, 158, 11); // Âmbar (#F59E0B)
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text("Produtos Mais Vendidos", margin, yPos);

        // Adicionar linha colorida abaixo do título
        doc.setDrawColor(245, 158, 11);
        doc.line(margin, yPos + 2, margin + 50, yPos + 2);

        // Tabela de produtos
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        autoTable(doc, {
          startY: yPos + 8,
          head: [['Produto', 'Qtd. Vendas', 'Valor Total', 'Variantes']],
          body: topProducts.map(product => [
            product.name,
            product.totalSales,
            formatCurrency(product.totalAmount),
            product.variantCount
          ]),
          headStyles: { fillColor: [245, 158, 11], textColor: [255, 255, 255] },
          alternateRowStyles: { fillColor: [245, 247, 250] },
          margin: { top: 10, right: margin, bottom: 10, left: margin },
          didDrawPage: function(data) {
            yPos = data.cursor.y + 5;
          }
        });
      }

      // Adicionar rodapé em todas as páginas
      const totalPages = doc.internal.pages.length - 1; // Corrigir contagem de páginas
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        addFooter(i, totalPages);
      }

      // Salvar o arquivo PDF
      doc.save(`relatorio-oluchys-${format(new Date(), "yyyy-MM-dd")}.pdf`);

      toast.success("Relatório gerado com sucesso", {
        description: "O download do arquivo PDF foi iniciado."
      });
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);

      // Exibir detalhes do erro para facilitar a depuração
      if (error instanceof Error) {
        console.error("Mensagem de erro:", error.message);
        console.error("Stack trace:", error.stack);
      }

      toast.error("Erro ao gerar PDF", {
        description: "Tente novamente ou contate o suporte."
      });
    }
  };

  const {
    filters,
    updateFilters,
    formattedDateRange,
    sellerReport,
    deliveryReport,
    paymentReport,
    topProducts,
    periodSummary,
    filterOptions,
    isLoading
  } = useReports();

  /**
   * Renderiza um estado de carregamento com skeletons
   * enquanto os dados estão sendo buscados
   */
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold mb-6">Relatórios</h1>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {/* Skeletons para os cards de métricas */}
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        {/* Skeleton para o gráfico principal */}
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Relatórios</h1>
        <Button variant="outline" onClick={generatePDF}>
          Exportar PDF
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <MetricCard
          title="Total de Vendas"
          value={periodSummary.totalSales}
          icon={ShoppingBag}
          iconColor="text-blue-600"
          description={<>
            {periodSummary.reservadas} <span className="text-amber-500 font-medium">reservadas</span>, {periodSummary.canceladas} <span className="text-red-500 font-medium">canceladas</span>
          </>}
        />
        <MetricCard
          title="Valor Total"
          value={formatCurrency(periodSummary.totalAmount)}
          icon={Banknote}
          iconColor="text-green-600"
          description="Valor total das vendas no período"
        />
        <MetricCard
          title="Vendedores Ativos"
          value={sellerReport.length}
          icon={Users}
          iconColor="text-purple-600"
          description="Vendedores com vendas no período"
        />
        <MetricCard
          title="Média por Venda"
          value={formatCurrency(periodSummary.totalAmount / (periodSummary.totalSales || 1))}
          icon={TrendingUp}
          iconColor="text-amber-600"
          description="Valor médio por venda no período"
        />
      </div>

      <Card className="mb-8">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">Filtros</CardTitle>
          <CardDescription>Período: {formattedDateRange()}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="period">Período</Label>
              <Select
                value={filters.period}
                onValueChange={(value) => updateFilters({ period: value as FilterPeriod })}
              >
                <SelectTrigger id="period">
                  <SelectValue placeholder="Selecione o período" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Hoje</SelectItem>
                  <SelectItem value="thisWeek">Esta Semana</SelectItem>
                  <SelectItem value="thisMonth">Este Mês</SelectItem>
                  <SelectItem value="custom">Personalizado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="startDate">Data Inicial</Label>
              <Input
                id="startDate"
                type="date"
                value={filters.startDate || ''}
                onChange={(e) => updateFilters({ startDate: e.target.value, period: 'custom' })}
              />
            </div>

            <div>
              <Label htmlFor="endDate">Data Final</Label>
              <Input
                id="endDate"
                type="date"
                value={filters.endDate || ''}
                onChange={(e) => updateFilters({ endDate: e.target.value, period: 'custom' })}
              />
            </div>

            <div>
              <Label htmlFor="seller">Vendedor</Label>
              <Select
                value={filters.sellerId || undefined}
                onValueChange={(value) => updateFilters({ sellerId: value || null })}
              >
                <SelectTrigger id="seller">
                  <SelectValue placeholder="Todos os vendedores" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os vendedores</SelectItem>
                  {filterOptions.sellers.map((seller) => (
                    <SelectItem key={seller.id} value={seller.id}>
                      {seller.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.statusId || undefined}
                onValueChange={(value) => updateFilters({ statusId: value || null })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Todos os status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  {filterOptions.statuses.map((status) => (
                    <SelectItem key={status.id} value={status.id}>
                      {status.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <Tabs defaultValue="sellers" onValueChange={setActiveTab}>
            <div className="overflow-x-auto pb-2 mt-6">
              <TabsList className="mb-6 inline-flex min-w-max">
                <TabsTrigger value="sellers" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Vendedores</span>
                </TabsTrigger>
                <TabsTrigger value="delivery" className="flex items-center gap-2">
                  <Truck className="h-4 w-4" />
                  <span>Entregadores</span>
                </TabsTrigger>
                <TabsTrigger value="products" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  <span>Produtos</span>
                </TabsTrigger>
                <TabsTrigger value="payments" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  <span>Formas de Pagamento</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="space-y-6">
              <TabsContent value="sellers">
                <div className="space-y-4">
                  <div>
                    <CardTitle className="mb-2">Relatório de Vendas por Vendedor</CardTitle>
                    <CardDescription className="mb-4">
                      Desempenho de vendas por vendedor no período selecionado
                    </CardDescription>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Vendedor</TableHead>
                          <TableHead className="text-right">Qtd. Vendas</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="text-right">Média por Venda</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {sellerReport.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center">
                              Nenhum dado encontrado para o período selecionado.
                            </TableCell>
                          </TableRow>
                        ) : (
                          sellerReport.map((seller) => (
                            <TableRow key={seller.id}>
                              <TableCell>{seller.name}</TableCell>
                              <TableCell className="text-right">{seller.totalSales}</TableCell>
                              <TableCell className="text-right">{formatCurrency(seller.totalAmount)}</TableCell>
                              <TableCell className="text-right">
                                {formatCurrency(seller.totalAmount / seller.totalSales)}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="delivery">
                <div className="space-y-4">
                  <div>
                    <CardTitle className="mb-2">Relatório de Entregas por Entregador</CardTitle>
                    <CardDescription className="mb-4">
                      Desempenho de entregas por entregador no período selecionado
                    </CardDescription>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Entregador</TableHead>
                          <TableHead className="text-right">Qtd. Entregas</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="text-right">Média por Entrega</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {deliveryReport.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center">
                              Nenhum dado encontrado para o período selecionado.
                            </TableCell>
                          </TableRow>
                        ) : (
                          deliveryReport.map((delivery) => (
                            <TableRow key={delivery.id}>
                              <TableCell>{delivery.name}</TableCell>
                              <TableCell className="text-right">{delivery.totalDeliveries}</TableCell>
                              <TableCell className="text-right">{formatCurrency(delivery.totalAmount)}</TableCell>
                              <TableCell className="text-right">
                                {formatCurrency(delivery.totalAmount / delivery.totalDeliveries)}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="products">
                <div className="space-y-4">
                  <div>
                    <CardTitle className="mb-2">Relatório de Produtos Mais Vendidos</CardTitle>
                    <CardDescription className="mb-4">
                      Produtos mais vendidos no período selecionado
                    </CardDescription>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Produto</TableHead>
                          <TableHead className="text-right">Qtd. Vendas</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead>Variantes</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {topProducts.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center">
                              Nenhum dado encontrado para o período selecionado.
                            </TableCell>
                          </TableRow>
                        ) : (
                          topProducts.map((product) => (
                            <TableRow key={product.id}>
                              <TableCell>{product.name}</TableCell>
                              <TableCell className="text-right">{product.totalSales}</TableCell>
                              <TableCell className="text-right">{formatCurrency(product.totalAmount)}</TableCell>
                              <TableCell>
                                <div className="flex flex-wrap gap-1">
                                  {product.variants.map((variant: any, index: number) => (
                                    <div
                                      key={index}
                                      className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded"
                                    >
                                      {variant.color}, {variant.size}
                                    </div>
                                  ))}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="payments">
                <div className="space-y-6">
                  <div>
                    <CardTitle className="mb-2">Relatório de Vendas por Forma de Pagamento</CardTitle>
                    <CardDescription className="mb-4">
                      Desempenho de vendas por método de pagamento no período selecionado
                    </CardDescription>
                  </div>

                  {/* Gráfico de pizza para distribuição de vendas por método de pagamento */}
                  {paymentReport.length > 0 && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">Distribuição por Valor</CardTitle>
                          <CardDescription>Proporção do valor total por método de pagamento</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <PaymentPieChart
                            data={paymentReport}
                            valueType="amount"
                            title=""
                          />
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">Distribuição por Quantidade</CardTitle>
                          <CardDescription>Proporção da quantidade de vendas por método de pagamento</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <PaymentPieChart
                            data={paymentReport}
                            valueType="sales"
                            title=""
                          />
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Método de Pagamento</TableHead>
                          <TableHead className="text-right">Qtd. Vendas</TableHead>
                          <TableHead className="text-right">Valor Total</TableHead>
                          <TableHead className="text-right">% do Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paymentReport.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center">
                              Nenhum dado encontrado para o período selecionado.
                            </TableCell>
                          </TableRow>
                        ) : (
                          paymentReport.map((payment) => (
                            <TableRow key={payment.id}>
                              <TableCell>{payment.name}</TableCell>
                              <TableCell className="text-right">{payment.totalSales}</TableCell>
                              <TableCell className="text-right">{formatCurrency(payment.totalAmount)}</TableCell>
                              <TableCell className="text-right">
                                {periodSummary.totalAmount > 0
                                  ? `${((payment.totalAmount / periodSummary.totalAmount) * 100).toFixed(1)}%`
                                  : '0%'
                                }
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="mb-2">Vendas do Período</CardTitle>
          <CardDescription>Valores por dia no período selecionado</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={periodSummary.dailySales}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="formattedDate"
                  tickFormatter={(value) => value.split('/').slice(0, 2).join('/')}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => {
                    // Usar formatador diferente dependendo do tipo de dado
                    if (name === "Vendas") {
                      return [Number(value).toString(), "Vendas"];
                    } else {
                      return [formatCurrency(Number(value)), "Valor"];
                    }
                  }}
                  labelFormatter={(label) => `Data: ${label}`}
                />
                <Legend />
                <Bar dataKey="amount" name="Valor Total" fill="#1E40AF" />
                <Bar dataKey="sales" name="Vendas" fill="#60A5FA" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default Reports;
