/**
 * @file Sales.tsx
 * @description Página de gerenciamento de vendas que permite visualizar, adicionar,
 * editar e filtrar vendas. Exibe estatísticas de vendas e fornece acesso à tabela
 * de vendas com funcionalidades de busca, filtragem e paginação.
 */

import React, { useState } from "react";
import { Plus, ShoppingBag } from "lucide-react";
import { Button } from "@/components/ui/button";
import SalesTable from "@/components/SalesTable";
import { useSales } from "@/hooks/use-sales";
import { useAuth } from "@/context/AuthContext";
import { formatCurrency } from "@/lib/utils";

/**
 * Página de gerenciamento de vendas
 *
 * @component
 * @returns {JSX.Element} Página de vendas renderizada
 */
const Sales = () => {
  // Obtém informações do usuário autenticado
  const { user, profile } = useAuth();
  // Obtém estatísticas de vendas e estado de carregamento
  const { stats, isLoading } = useSales();

  /**
   * Verifica se o usuário está autenticado
   * Exibe uma mensagem solicitando login caso não esteja
   */
  if (!user || !profile) {
    return (
      <div className="p-6 max-w-7xl mx-auto text-center">
        <p className="text-gray-500 dark:text-gray-400">
          Faça login para acessar esta página.
        </p>
      </div>
    );
  }

  // Determina se o usuário tem permissões de administrador
  const isAdmin = profile.role === 'admin';

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-xl font-bold">Controle de Vendas</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Gerenciamento de vendas e pedidos
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-4">
            <ShoppingBag className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Realizadas</p>
            <h3 className="text-lg font-semibold">{stats.realizadas}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center mr-4">
            <ShoppingBag className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Reservadas</p>
            <h3 className="text-lg font-semibold">{stats.reservadas}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-4">
            <ShoppingBag className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Canceladas</p>
            <h3 className="text-lg font-semibold">{stats.canceladas}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-4">
            <ShoppingBag className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Total</p>
            <h3 className="text-1.25rem font-semibold">{formatCurrency(stats.totalValue)}</h3>
          </div>
        </div>
      </div>

      <SalesTable
        currentUser={profile.name}
        isAdmin={isAdmin}
      />
    </div>
  );
};

export default Sales;
