/**
 * @file Settings.tsx
 * @description Página de configurações do sistema que permite aos administradores
 * gerenciar informações fixas como cores, tamanhos, categorias, status, formas de pagamento
 * e entregadores. Inclui controle de acesso para garantir que apenas administradores possam
 * acessar estas configurações.
 */

import React, { useEffect } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Palette, Ruler, Package, Check, CreditCard, Truck } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import ColorSettings from "@/components/settings/ColorSettings";
import SizeSettings from "@/components/settings/SizeSettings";
import CategorySettings from "@/components/settings/CategorySettings";
import StatusSettings from "@/components/settings/StatusSettings";
import PaymentMethodSettings from "@/components/settings/PaymentMethodSettings";
import DeliveryPersonsSettings from "@/components/settings/DeliveryPersonsSettings";

/**
 * Página de configurações do sistema
 *
 * @component
 * @returns {JSX.Element} Página de configurações renderizada
 */
const Settings = () => {
  const navigate = useNavigate();
  const { user } = useAuth(); // Obtém informações do usuário autenticado

  /**
   * Efeito para verificar se o usuário tem permissões de administrador
   * Redireciona para a página de login ou para a página inicial caso o usuário
   * não esteja autenticado ou não tenha permissões de administrador
   */
  useEffect(() => {
    const checkAdminRole = async () => {
      // Verifica se o usuário está autenticado
      if (!user) {
        toast.error("Acesso negado", {
          description: "Você precisa estar logado para acessar esta página"
        });
        navigate("/login");
        return;
      }

      try {
        // Consulta o perfil do usuário para verificar a função (role)
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        // Verifica se o usuário é administrador
        if (data.role !== 'admin') {
          toast.error("Acesso negado", {
            description: "Apenas administradores podem acessar esta página"
          });
          navigate("/");
        }
      } catch (error) {
        console.error("Erro ao verificar permissões:", error);
        toast.error("Erro", {
          description: "Ocorreu um erro ao verificar suas permissões"
        });
        navigate("/");
      }
    };

    checkAdminRole();
  }, [user, navigate]); // Executa quando o usuário ou a função de navegação mudam

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Informações Fixas</h1>

      <Tabs defaultValue="colors" className="w-full">
        <div className="overflow-x-auto pb-2">
          <TabsList className="inline-flex whitespace-nowrap min-w-max mb-8">
            <TabsTrigger value="colors" className="flex gap-2 items-center">
              <Palette className="h-4 w-4" />
              <span>Cores</span>
            </TabsTrigger>
            <TabsTrigger value="sizes" className="flex gap-2 items-center">
              <Ruler className="h-4 w-4" />
              <span>Tamanhos</span>
            </TabsTrigger>
            <TabsTrigger value="categories" className="flex gap-2 items-center">
              <Package className="h-4 w-4" />
              <span>Categorias</span>
            </TabsTrigger>
            <TabsTrigger value="statuses" className="flex gap-2 items-center">
              <Check className="h-4 w-4" />
              <span>Status</span>
            </TabsTrigger>
            <TabsTrigger value="payment-methods" className="flex gap-2 items-center">
              <CreditCard className="h-4 w-4" />
              <span>Formas de Pagamento</span>
            </TabsTrigger>
            <TabsTrigger value="delivery-persons" className="flex gap-2 items-center">
              <Truck className="h-4 w-4" />
              <span>Motoqueiros</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="colors" className="border rounded-lg p-6 py-[40px]">
          <ColorSettings />
        </TabsContent>

        <TabsContent value="sizes" className="border rounded-lg p-6">
          <SizeSettings />
        </TabsContent>

        <TabsContent value="categories" className="border rounded-lg p-6">
          <CategorySettings />
        </TabsContent>

        <TabsContent value="statuses" className="border rounded-lg p-6">
          <StatusSettings />
        </TabsContent>

        <TabsContent value="payment-methods" className="border rounded-lg p-6">
          <PaymentMethodSettings />
        </TabsContent>

        <TabsContent value="delivery-persons" className="border rounded-lg p-6">
          <DeliveryPersonsSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
