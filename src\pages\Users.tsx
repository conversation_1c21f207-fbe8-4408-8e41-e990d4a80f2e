/**
 * @file Users.tsx
 * @description Página de gerenciamento de usuários que permite aos administradores
 * visualizar, adicionar, editar e filtrar usuários do sistema. Inclui controle de acesso
 * para garantir que apenas administradores possam acessar esta página.
 */

import React, { useState, useCallback } from "react";
import { Plus, Users as UsersIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import UsersTable from "@/components/UsersTable";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import NewUserForm from "@/components/NewUserForm";
import { toast } from "sonner";
import { fetchUsers, createUser, isUserAdmin } from "@/services/userService";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";

export type UserRole = "admin" | "vendedor";

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  active: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Página de gerenciamento de usuários
 *
 * @component
 * @returns {JSX.Element} Página de usuários renderizada
 */
const Users = () => {
  // Estados para controlar o diálogo de novo usuário e a aba ativa
  const [openNewUserDialog, setOpenNewUserDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("all");

  // Hooks para navegação e autenticação
  const navigate = useNavigate();
  const { user } = useAuth();

  /**
   * Função para verificar se o usuário tem permissões de administrador
   * Redireciona para a página inicial caso não tenha permissões
   *
   * @returns {Promise<boolean>} true se o usuário for administrador, false caso contrário
   */
  const checkAdminPermission = useCallback(async () => {
    try {
      const admin = await isUserAdmin();
      if (!admin) {
        toast.error("Acesso negado. Esta página é restrita a administradores.");
        navigate("/");
        return false;
      }
      return true;
    } catch (error: any) {
      console.error("Erro ao verificar permissões:", error);
      toast.error(`Erro ao verificar permissões: ${error.message}`);
      return false;
    }
  }, [navigate]);

  /**
   * Efeito para verificar permissões ao montar o componente
   */
  React.useEffect(() => {
    checkAdminPermission();
  }, [checkAdminPermission]);

  /**
   * Consulta para buscar dados de usuários usando React Query
   * Configuração com tempo de atualização e cache
   */
  const {
    data: users = [], // Lista de usuários com valor padrão de array vazio
    isLoading, // Estado de carregamento
    isError, // Estado de erro
    error, // Detalhes do erro, se houver
    refetch // Função para recarregar os dados
  } = useQuery({
    queryKey: ['users'],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // Considera dados atuais por 5 minutos
  });

  /**
   * Manipula a criação de um novo usuário
   *
   * @param {Object} userData - Dados do novo usuário
   * @param {string} userData.name - Nome do usuário
   * @param {string} userData.email - Email do usuário
   * @param {string} userData.password - Senha do usuário
   * @param {string} userData.phone - Telefone do usuário
   * @param {UserRole} userData.role - Função do usuário (admin ou seller)
   * @param {boolean} userData.active - Estado de ativação do usuário
   */
  const handleNewUser = async (userData: {
    name: string;
    email: string;
    password: string;
    phone: string;
    role: UserRole;
    active: boolean;
  }) => {
    try {
      // Cria o usuário usando o serviço
      await createUser(userData);
      // Recarrega a lista de usuários
      refetch();
      // Exibe mensagem de sucesso
      toast.success("Usuário adicionado com sucesso!");
      // Fecha o diálogo
      setOpenNewUserDialog(false);
    } catch (error: any) {
      console.error("Erro ao adicionar usuário:", error);
      toast.error(`Erro ao adicionar usuário: ${error.message}`);
    }
  };

  /**
   * Função para atualizar a lista de usuários
   * Usada após operações de edição ou exclusão
   */
  const refreshUsers = async () => {
    await refetch();
  };

  const filteredUsers = users.filter(user => {
    if (activeTab === "all") return true;
    if (activeTab === "admins") return user.role === "admin";
    if (activeTab === "sellers") return user.role === "vendedor";
    if (activeTab === "inactive") return !user.active;
    return true;
  });

  if (isLoading) {
    return (
      <div className="p-6 max-w-7xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-32 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-10 w-40" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-24 rounded-xl" />
          ))}
        </div>

        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-[400px] rounded-xl" />
      </div>
    );
  }

  if (isError && error) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md mb-6">
          <h3 className="text-lg font-medium">Erro ao carregar dados</h3>
          <p>Ocorreu um erro ao carregar os usuários. Por favor, tente novamente mais tarde.</p>
          <Button
            variant="outline"
            className="mt-2"
            onClick={() => refetch()}
          >
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-2xl font-bold">Usuários</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Gerenciamento de usuários e vendedores
          </p>
        </div>

        <Button className="gap-1" onClick={() => setOpenNewUserDialog(true)}>
          <Plus className="h-4 w-4" />
          Adicionar Usuário
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-4">
            <UsersIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Usuários</p>
            <h3 className="text-2xl font-semibold">{users.length}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mr-4">
            <UsersIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Admins</p>
            <h3 className="text-2xl font-semibold">{users.filter(u => u.role === "admin").length}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-4">
            <UsersIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Vendedores</p>
            <h3 className="text-2xl font-semibold">{users.filter(u => u.role === "vendedor").length}</h3>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-card border border-gray-100 dark:border-gray-800 flex items-center">
          <div className="w-12 h-12 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-4">
            <UsersIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Inativos</p>
            <h3 className="text-2xl font-semibold">{users.filter(u => !u.active).length}</h3>
          </div>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">Todos</TabsTrigger>
          <TabsTrigger value="admins">Admins</TabsTrigger>
          <TabsTrigger value="sellers">Vendedores</TabsTrigger>
          <TabsTrigger value="inactive">Inativos</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          <UsersTable
            users={filteredUsers}
            onRefresh={refreshUsers}
          />
        </TabsContent>

        <TabsContent value="admins" className="mt-0">
          <UsersTable
            users={filteredUsers}
            onRefresh={refreshUsers}
          />
        </TabsContent>

        <TabsContent value="sellers" className="mt-0">
          <UsersTable
            users={filteredUsers}
            onRefresh={refreshUsers}
          />
        </TabsContent>

        <TabsContent value="inactive" className="mt-0">
          <UsersTable
            users={filteredUsers}
            onRefresh={refreshUsers}
          />
        </TabsContent>
      </Tabs>

      <Dialog open={openNewUserDialog} onOpenChange={setOpenNewUserDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Novo Usuário</DialogTitle>
            <DialogDescription>
              Preencha os detalhes para adicionar um novo usuário ao sistema.
            </DialogDescription>
          </DialogHeader>
          <NewUserForm onSubmit={handleNewUser} onCancel={() => setOpenNewUserDialog(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Users;
