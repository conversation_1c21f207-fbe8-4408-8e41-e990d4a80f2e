
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Category {
  id: string;
  name: string;
}

export const fetchCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('product_categories')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    return data.map(category => ({
      id: category.id,
      name: category.name
    }));
  } catch (error) {
    console.error("Erro ao buscar categorias:", error);
    toast.error("Erro ao carregar categorias");
    return [];
  }
};

export const addCategory = async (category: Omit<Category, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('product_categories')
      .insert({ name: category.name })
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Categoria adicionada com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao adicionar categoria:", error);
    toast.error("Erro ao adicionar categoria");
    throw error;
  }
};

export const updateCategory = async (category: Category) => {
  try {
    const { data, error } = await supabase
      .from('product_categories')
      .update({ name: category.name })
      .eq('id', category.id)
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Categoria atualizada com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao atualizar categoria:", error);
    toast.error("Erro ao atualizar categoria");
    throw error;
  }
};

export const deleteCategory = async (id: string) => {
  try {
    const { error } = await supabase
      .from('product_categories')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    toast.success("Categoria removida com sucesso");
    return true;
  } catch (error) {
    console.error("Erro ao remover categoria:", error);
    toast.error("Erro ao remover categoria");
    throw error;
  }
};
