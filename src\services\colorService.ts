
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Color {
  id: string;
  name: string;
  hexCode: string;
}

export const fetchColors = async () => {
  try {
    const { data, error } = await supabase
      .from('colors')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    return data.map(color => ({
      id: color.id,
      name: color.name,
      hexCode: color.hex_code || '#000000'
    }));
  } catch (error) {
    console.error("Erro ao buscar cores:", error);
    toast.error("Erro ao carregar cores");
    return [];
  }
};

export const addColor = async (color: Omit<Color, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('colors')
      .insert({ 
        name: color.name, 
        hex_code: color.hexCode 
      })
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Cor adicionada com sucesso");
    return {
      id: data.id,
      name: data.name,
      hexCode: data.hex_code || '#000000'
    };
  } catch (error) {
    console.error("Erro ao adicionar cor:", error);
    toast.error("Erro ao adicionar cor");
    throw error;
  }
};

export const updateColor = async (color: Color) => {
  try {
    const { data, error } = await supabase
      .from('colors')
      .update({ 
        name: color.name, 
        hex_code: color.hexCode 
      })
      .eq('id', color.id)
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Cor atualizada com sucesso");
    return {
      id: data.id,
      name: data.name,
      hexCode: data.hex_code || '#000000'
    };
  } catch (error) {
    console.error("Erro ao atualizar cor:", error);
    toast.error("Erro ao atualizar cor");
    throw error;
  }
};

export const deleteColor = async (id: string) => {
  try {
    const { error } = await supabase
      .from('colors')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    toast.success("Cor removida com sucesso");
    return true;
  } catch (error) {
    console.error("Erro ao remover cor:", error);
    toast.error("Erro ao remover cor");
    throw error;
  }
};
