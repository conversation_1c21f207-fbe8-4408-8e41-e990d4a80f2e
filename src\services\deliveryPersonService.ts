
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface DeliveryPerson {
  id: string;
  name: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

// Buscar todos os motoqueiros
export const fetchDeliveryPersons = async (): Promise<DeliveryPerson[]> => {
  try {
    const { data, error } = await supabase
      .from('delivery_persons')
      .select('*')
      .order('name');
      
    if (error) throw error;
    
    return data as DeliveryPerson[];
  } catch (error: any) {
    console.error("Erro ao buscar motoqueiros:", error.message);
    toast.error(`Erro ao carregar motoqueiros: ${error.message}`);
    return [];
  }
};

// Adicionar um novo motoqueiro
export const addDeliveryPerson = async (
  data: Omit<DeliveryPerson, 'id' | 'created_at' | 'updated_at'>
): Promise<DeliveryPerson> => {
  try {
    const { data: newData, error } = await supabase
      .from('delivery_persons')
      .insert(data)
      .select()
      .single();
      
    if (error) throw error;
    
    return newData as DeliveryPerson;
  } catch (error: any) {
    console.error("Erro ao adicionar motoqueiro:", error.message);
    toast.error(`Erro ao adicionar motoqueiro: ${error.message}`);
    throw error;
  }
};

// Atualizar um motoqueiro existente
export const updateDeliveryPerson = async (
  id: string,
  data: Partial<Omit<DeliveryPerson, 'id' | 'created_at' | 'updated_at'>>
): Promise<DeliveryPerson> => {
  try {
    const { data: updatedData, error } = await supabase
      .from('delivery_persons')
      .update(data)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    
    return updatedData as DeliveryPerson;
  } catch (error: any) {
    console.error("Erro ao atualizar motoqueiro:", error.message);
    toast.error(`Erro ao atualizar motoqueiro: ${error.message}`);
    throw error;
  }
};

// Deletar um motoqueiro
export const deleteDeliveryPerson = async (id: string): Promise<boolean> => {
  try {
    // Verificar se o motoqueiro está sendo usado em alguma venda
    const { data: salesData, error: salesError } = await supabase
      .from('sales')
      .select('id')
      .eq('delivery_person_id', id)
      .limit(1);

    if (salesError) throw salesError;

    if (salesData && salesData.length > 0) {
      throw new Error("Este motoqueiro não pode ser excluído porque está associado a vendas");
    }
    
    const { error } = await supabase
      .from('delivery_persons')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    return true;
  } catch (error: any) {
    console.error("Erro ao deletar motoqueiro:", error.message);
    toast.error(`Erro ao deletar motoqueiro: ${error.message}`);
    throw error;
  }
};
