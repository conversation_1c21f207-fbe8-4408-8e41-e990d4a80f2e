
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface PaymentMethod {
  id: string;
  name: string;
}

export const fetchPaymentMethods = async () => {
  try {
    const { data, error } = await supabase
      .from('payment_methods')
      .select('*')
      .order('name');
      
    if (error) throw error;
    
    return data.map(method => ({
      id: method.id,
      name: method.name
    }));
  } catch (error) {
    console.error("Erro ao buscar formas de pagamento:", error);
    toast.error("Erro ao carregar formas de pagamento");
    return [];
  }
};

export const addPaymentMethod = async (method: Omit<PaymentMethod, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('payment_methods')
      .insert({ name: method.name })
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Forma de pagamento adicionada com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao adicionar forma de pagamento:", error);
    toast.error("Erro ao adicionar forma de pagamento");
    throw error;
  }
};

export const updatePaymentMethod = async (method: PaymentMethod) => {
  try {
    const { data, error } = await supabase
      .from('payment_methods')
      .update({ name: method.name })
      .eq('id', method.id)
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Forma de pagamento atualizada com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao atualizar forma de pagamento:", error);
    toast.error("Erro ao atualizar forma de pagamento");
    throw error;
  }
};

export const deletePaymentMethod = async (id: string) => {
  try {
    const { error } = await supabase
      .from('payment_methods')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    toast.success("Forma de pagamento removida com sucesso");
    return true;
  } catch (error) {
    console.error("Erro ao remover forma de pagamento:", error);
    toast.error("Erro ao remover forma de pagamento");
    throw error;
  }
};
