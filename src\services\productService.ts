
import { localSupabase } from "@/lib/localClient";
import { LocalStorageDB } from "@/lib/localStorage";
import { toast } from "sonner";
import { Product, ProductVariant, ProductCategory, Color, Size } from "@/types";

// Função para buscar todos os produtos
export const fetchProducts = async () => {
  try {
    const { data: products, error: productsError } = await LocalStorageDB.findAll<Product>(
      LocalStorageDB.STORAGE_KEYS.PRODUCTS
    );

    if (productsError) throw productsError;

    // Buscar categorias para enriquecer os dados
    const { data: categories, error: categoriesError } = await LocalStorageDB.findAll<ProductCategory>(
      LocalStorageDB.STORAGE_KEYS.CATEGORIES
    );

    if (categoriesError) {
      console.error("Erro ao buscar categorias:", categoriesError);
      return products;
    }

    // Enriquecer produtos com dados da categoria
    const enrichedProducts = products.map(product => {
      const category = categories.find(cat => cat.id === product.category_id);
      return {
        ...product,
        category: category ? { id: category.id, name: category.name } : undefined
      };
    });

    return enrichedProducts as Product[];
  } catch (error) {
    console.error("Erro ao buscar produtos:", error);
    toast.error("Erro ao carregar produtos");
    return [];
  }
};

// Função para buscar variações de produtos com detalhes
export const fetchProductVariants = async () => {
  try {
    // Buscar todas as entidades necessárias
    const { data: variants, error: variantsError } = await LocalStorageDB.findAll<ProductVariant>(
      LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS
    );

    if (variantsError) throw variantsError;

    const { data: products, error: productsError } = await LocalStorageDB.findAll<Product>(
      LocalStorageDB.STORAGE_KEYS.PRODUCTS
    );

    if (productsError) throw productsError;

    const { data: categories, error: categoriesError } = await LocalStorageDB.findAll<ProductCategory>(
      LocalStorageDB.STORAGE_KEYS.CATEGORIES
    );

    if (categoriesError) {
      console.error("Erro ao buscar categorias:", categoriesError);
    }

    const { data: colors, error: colorsError } = await LocalStorageDB.findAll<Color>(
      LocalStorageDB.STORAGE_KEYS.COLORS
    );

    if (colorsError) {
      console.error("Erro ao buscar cores:", colorsError);
    }

    const { data: sizes, error: sizesError } = await LocalStorageDB.findAll<Size>(
      LocalStorageDB.STORAGE_KEYS.SIZES
    );

    if (sizesError) {
      console.error("Erro ao buscar tamanhos:", sizesError);
    }

    // Enriquecer as variantes com suas relações
    const enrichedVariants = variants.map(variant => {
      const product = products.find(p => p.id === variant.product_id);
      const color = colors.find(c => c.id === variant.color_id);
      const size = sizes.find(s => s.id === variant.size_id);

      let enrichedProduct = product;
      if (product && categories) {
        const category = categories.find(c => c.id === product.category_id);
        enrichedProduct = {
          ...product,
          category: category ? { id: category.id, name: category.name } : undefined
        };
      }

      return {
        ...variant,
        product: enrichedProduct,
        color: color ? {
          id: color.id,
          name: color.name,
          hex_code: color.hex_code
        } : undefined,
        size: size ? {
          id: size.id,
          name: size.name
        } : undefined
      };
    });

    return enrichedVariants as ProductVariant[];
  } catch (error) {
    console.error("Erro ao buscar variações de produtos:", error);
    toast.error("Erro ao carregar variações de produtos");
    return [];
  }
};

// Função para buscar produtos com estoque baixo
export const fetchLowStockProducts = async () => {
  try {
    // Buscar variantes com estoque baixo (menos de 10 unidades)
    const { data: variants, error: variantsError } = await LocalStorageDB.findWhere<ProductVariant>(
      LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
      (variant) => variant.quantity < 10
    );

    if (variantsError) throw variantsError;

    // Buscar dados relacionados para enriquecer as variantes
    const { data: products, error: productsError } = await LocalStorageDB.findAll<Product>(
      LocalStorageDB.STORAGE_KEYS.PRODUCTS
    );

    if (productsError) throw productsError;

    const { data: categories, error: categoriesError } = await LocalStorageDB.findAll<ProductCategory>(
      LocalStorageDB.STORAGE_KEYS.CATEGORIES
    );

    if (categoriesError) {
      console.error("Erro ao buscar categorias:", categoriesError);
    }

    const { data: colors, error: colorsError } = await LocalStorageDB.findAll<Color>(
      LocalStorageDB.STORAGE_KEYS.COLORS
    );

    if (colorsError) {
      console.error("Erro ao buscar cores:", colorsError);
    }

    const { data: sizes, error: sizesError } = await LocalStorageDB.findAll<Size>(
      LocalStorageDB.STORAGE_KEYS.SIZES
    );

    if (sizesError) {
      console.error("Erro ao buscar tamanhos:", sizesError);
    }

    // Enriquecer as variantes com suas relações
    const enrichedVariants = variants.map(variant => {
      const product = products.find(p => p.id === variant.product_id);
      const color = colors.find(c => c.id === variant.color_id);
      const size = sizes.find(s => s.id === variant.size_id);

      let enrichedProduct = product;
      if (product && categories) {
        const category = categories.find(c => c.id === product.category_id);
        enrichedProduct = {
          ...product,
          category: category ? { id: category.id, name: category.name } : undefined
        };
      }

      return {
        ...variant,
        product: enrichedProduct,
        color: color ? {
          id: color.id,
          name: color.name,
          hex_code: color.hex_code
        } : undefined,
        size: size ? {
          id: size.id,
          name: size.name
        } : undefined
      };
    });
    return enrichedVariants as ProductVariant[];
  } catch (error) {
    console.error("Erro ao buscar produtos com estoque baixo:", error);
    toast.error("Erro ao carregar produtos com estoque baixo");
    return [];
  }
};

// Função para adicionar um novo produto
export const addProduct = async (productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const { data, error } = await LocalStorageDB.create<Product>(
      LocalStorageDB.STORAGE_KEYS.PRODUCTS,
      productData
    )
    if (error) throw error;

    return data as Product;
  } catch (error) {
    console.error("Erro ao adicionar produto:", error);
    toast.error("Erro ao adicionar produto");
    throw error;
  }
};

// Função para adicionar uma variação de produto
export const addProductVariant = async (variantData: Omit<ProductVariant, 'id' | 'created_at' | 'updated_at' | 'product' | 'color' | 'size'>) => {
  try {
    const { data, error } = await LocalStorageDB.create<ProductVariant>(
      LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
      variantData
    );

    if (error) throw error;

    return data;
  } catch (error) {
    console.error("Erro ao adicionar variação de produto:", error);
    toast.error("Erro ao adicionar variação de produto");
    throw error;
  }
};

// Função para atualizar uma variação de produto
export const updateProductVariant = async (id: string, variantData: Partial<Omit<ProductVariant, 'id' | 'created_at' | 'updated_at' | 'product' | 'color' | 'size'>>) => {
  try {
    // Verificar se os IDs estão presentes e não são strings vazias
    if (variantData.color_id === "") {
      throw new Error("ID da cor não pode ser vazio");
    }

    if (variantData.size_id === "") {
      throw new Error("ID do tamanho não pode ser vazio");
    }

    const { data, error } = await LocalStorageDB.update<ProductVariant>(
      LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
      id,
      variantData
    );

    if (error) throw error;

    return data;
  } catch (error) {
    console.error("Erro ao atualizar variação de produto:", error);
    throw error;
  }
};

// Função para atualizar um produto
export const updateProduct = async (id: string, productData: Partial<Omit<Product, 'id' | 'created_at' | 'updated_at' | 'category'>>) => {
  try {
    // Verificar se o ID da categoria está presente e não é uma string vazia
    if (productData.category_id === "") {
      throw new Error("ID da categoria não pode ser vazio");
    }

    // Verificar se o ID é válido
    if (!id || id === "") {
      throw new Error("ID do produto não pode ser vazio");
    }

    console.log("Atualizando produto com ID:", id);
    console.log("Dados da atualização:", productData);

    const { data, error } = await LocalStorageDB.update<Product>(
      LocalStorageDB.STORAGE_KEYS.PRODUCTS,
      id,
      productData
    );

    if (error) throw error;

    return data as Product;
  } catch (error) {
    console.error("Erro ao atualizar produto:", error);
    throw error;
  }
};

// Função para deletar uma variação de produto
export const deleteProductVariant = async (id: string) => {
  try {
    const { error } = await LocalStorageDB.delete<ProductVariant>(
      LocalStorageDB.STORAGE_KEYS.PRODUCT_VARIANTS,
      id
    );

    if (error) throw error;

    return true;
  } catch (error) {
    console.error("Erro ao remover variação de produto:", error);
    toast.error("Erro ao remover variação de produto");
    throw error;
  }
};
