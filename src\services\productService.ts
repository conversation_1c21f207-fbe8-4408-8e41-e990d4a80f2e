
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Product, ProductVariant } from "@/types";

// Função para buscar todos os produtos
export const fetchProducts = async () => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:category_id(id, name)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data as Product[];
  } catch (error) {
    console.error("Erro ao buscar produtos:", error);
    toast.error("Erro ao carregar produtos");
    return [];
  }
};

// Função para buscar variações de produtos com detalhes
export const fetchProductVariants = async () => {
  try {
    // Primeiro, buscar todas as categorias para garantir que estejam em cache
    const { data: categories, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*');

    if (categoriesError) {
      console.error("Erro ao buscar categorias:", categoriesError);
    }

    // Agora buscar as variações de produtos com suas relações
    const { data, error } = await supabase
      .from('product_variants')
      .select(`
        *,
        product:product_id(id, name, price, category_id, category:category_id(id, name)),
        color:color_id(id, name, hex_code),
        size:size_id(id, name)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Processar os dados para garantir que as categorias estejam corretamente associadas
    const processedData = data.map(variant => {
      // Se o produto tem category_id mas não tem category, buscar a categoria do cache
      if (variant.product && variant.product.category_id && !variant.product.category) {
        const category = categories?.find(cat => cat.id === variant.product.category_id);
        if (category) {
          variant.product.category = {
            id: category.id,
            name: category.name
          };
        }
      }
      return variant;
    });

    return processedData as ProductVariant[];
  } catch (error) {
    console.error("Erro ao buscar variações de produtos:", error);
    toast.error("Erro ao carregar variações de produtos");
    return [];
  }
};

// Função para buscar produtos com estoque baixo
export const fetchLowStockProducts = async () => {
  try {
    // Primeiro, buscar todas as categorias para garantir que estejam em cache
    const { data: categories, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*');

    if (categoriesError) {
      console.error("Erro ao buscar categorias:", categoriesError);
    }

    const { data, error } = await supabase
      .from('product_variants')
      .select(`
        *,
        product:product_id(id, name, price, category_id, category:category_id(id, name)),
        color:color_id(id, name, hex_code),
        size:size_id(id, name)
      `)
      .lte('quantity', 5) // Estoque baixo é 5 ou menos
      .order('quantity');

    if (error) throw error;

    // Processar os dados para garantir que as categorias estejam corretamente associadas
    const processedData = data.map(variant => {
      // Se o produto tem category_id mas não tem category, buscar a categoria do cache
      if (variant.product && variant.product.category_id && !variant.product.category) {
        const category = categories?.find(cat => cat.id === variant.product.category_id);
        if (category) {
          variant.product.category = {
            id: category.id,
            name: category.name
          };
        }
      }
      return variant;
    });

    return processedData as ProductVariant[];
  } catch (error) {
    console.error("Erro ao buscar produtos com estoque baixo:", error);
    toast.error("Erro ao carregar produtos com estoque baixo");
    return [];
  }
};

// Função para adicionar um novo produto
export const addProduct = async (productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (error) throw error;

    return data as Product;
  } catch (error) {
    console.error("Erro ao adicionar produto:", error);
    toast.error("Erro ao adicionar produto");
    throw error;
  }
};

// Função para adicionar uma variação de produto
export const addProductVariant = async (variantData: Omit<ProductVariant, 'id' | 'created_at' | 'updated_at' | 'product' | 'color' | 'size'>) => {
  try {
    const { data, error } = await supabase
      .from('product_variants')
      .insert(variantData)
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error("Erro ao adicionar variação de produto:", error);
    toast.error("Erro ao adicionar variação de produto");
    throw error;
  }
};

// Função para atualizar uma variação de produto
export const updateProductVariant = async (id: string, variantData: Partial<Omit<ProductVariant, 'id' | 'created_at' | 'updated_at' | 'product' | 'color' | 'size'>>) => {
  try {
    // Verificar se os IDs estão presentes e não são strings vazias
    if (variantData.color_id === "") {
      throw new Error("ID da cor não pode ser vazio");
    }

    if (variantData.size_id === "") {
      throw new Error("ID do tamanho não pode ser vazio");
    }

    const { data, error } = await supabase
      .from('product_variants')
      .update(variantData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error("Erro ao atualizar variação de produto:", error);
    throw error;
  }
};

// Função para atualizar um produto
export const updateProduct = async (id: string, productData: Partial<Omit<Product, 'id' | 'created_at' | 'updated_at' | 'category'>>) => {
  try {
    // Verificar se o ID da categoria está presente e não é uma string vazia
    if (productData.category_id === "") {
      throw new Error("ID da categoria não pode ser vazio");
    }

    // Verificar se o ID é válido
    if (!id || id === "") {
      throw new Error("ID do produto não pode ser vazio");
    }

    console.log("Atualizando produto com ID:", id);
    console.log("Dados da atualização:", productData);

    const { data, error } = await supabase
      .from('products')
      .update(productData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return data as Product;
  } catch (error) {
    console.error("Erro ao atualizar produto:", error);
    throw error;
  }
};

// Função para deletar uma variação de produto
export const deleteProductVariant = async (id: string) => {
  try {
    const { error } = await supabase
      .from('product_variants')
      .delete()
      .eq('id', id);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error("Erro ao remover variação de produto:", error);
    toast.error("Erro ao remover variação de produto");
    throw error;
  }
};
