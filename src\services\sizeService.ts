
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Size {
  id: string;
  name: string;
}

export const fetchSizes = async () => {
  try {
    const { data, error } = await supabase
      .from('sizes')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    return data.map(size => ({
      id: size.id,
      name: size.name
    }));
  } catch (error) {
    console.error("Erro ao buscar tamanhos:", error);
    toast.error("Erro ao carregar tamanhos");
    return [];
  }
};

export const addSize = async (size: Omit<Size, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('sizes')
      .insert({ name: size.name })
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Tamanho adicionado com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao adicionar tamanho:", error);
    toast.error("Erro ao adicionar tamanho");
    throw error;
  }
};

export const updateSize = async (size: Size) => {
  try {
    const { data, error } = await supabase
      .from('sizes')
      .update({ name: size.name })
      .eq('id', size.id)
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Tamanho atualizado com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao atualizar tamanho:", error);
    toast.error("Erro ao atualizar tamanho");
    throw error;
  }
};

export const deleteSize = async (id: string) => {
  try {
    const { error } = await supabase
      .from('sizes')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    toast.success("Tamanho removido com sucesso");
    return true;
  } catch (error) {
    console.error("Erro ao remover tamanho:", error);
    toast.error("Erro ao remover tamanho");
    throw error;
  }
};
