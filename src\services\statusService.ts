
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Status {
  id: string;
  name: string;
}

export const fetchStatuses = async () => {
  try {
    const { data, error } = await supabase
      .from('sale_statuses')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    
    return data.map(status => ({
      id: status.id,
      name: status.name
    }));
  } catch (error) {
    console.error("Erro ao buscar status:", error);
    toast.error("Erro ao carregar status");
    return [];
  }
};

export const addStatus = async (status: Omit<Status, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('sale_statuses')
      .insert({ name: status.name })
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Status adicionado com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao adicionar status:", error);
    toast.error("Erro ao adicionar status");
    throw error;
  }
};

export const updateStatus = async (status: Status) => {
  try {
    const { data, error } = await supabase
      .from('sale_statuses')
      .update({ name: status.name })
      .eq('id', status.id)
      .select()
      .single();
      
    if (error) throw error;
    
    toast.success("Status atualizado com sucesso");
    return {
      id: data.id,
      name: data.name
    };
  } catch (error) {
    console.error("Erro ao atualizar status:", error);
    toast.error("Erro ao atualizar status");
    throw error;
  }
};

export const deleteStatus = async (id: string) => {
  try {
    const { error } = await supabase
      .from('sale_statuses')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    
    toast.success("Status removido com sucesso");
    return true;
  } catch (error) {
    console.error("Erro ao remover status:", error);
    toast.error("Erro ao remover status");
    throw error;
  }
};
