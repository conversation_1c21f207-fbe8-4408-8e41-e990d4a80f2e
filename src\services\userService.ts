
import { localSupabase } from "@/lib/localClient";
import { LocalStorageDB } from "@/lib/localStorage";
import { Profile } from "@/types";

// Tipo para compatibilidade com a página Users
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'vendedor';
  active: boolean;
  created_at?: string;
  updated_at?: string;
}

export type UserRole = 'admin' | 'vendedor';

// Mapeamento fixo de emails para a versão demo
const EMAIL_MAPPING: Record<string, string> = {
  'admin': '<EMAIL>',
  'vendedor': '<EMAIL>'
};

// Função para obter emails dos usuários (versão local)
async function getUserEmails() {
  try {
    const { data: { session } } = await localSupabase.auth.getSession();
    if (!session) return {};

    // Na versão demo, retornamos o mapeamento fixo
    const profiles = await LocalStorageDB.findAll<Profile>(LocalStorageDB.STORAGE_KEYS.PROFILES);

    if (profiles.error) {
      console.error("Erro ao buscar perfis:", profiles.error);
      return {};
    }

    const emailMap: Record<string, string> = {};
    profiles.data.forEach((profile: Profile) => {
      // Mapear baseado no role
      const email = EMAIL_MAPPING[profile.role] || `${profile.role}@demo.com`;
      emailMap[profile.id] = email;
    });

    return emailMap;
      return {};
    }
  } catch (error) {
    console.error("Erro ao buscar emails dos usuários:", error);
    return {};
  }
}

// Buscar todos os usuários do perfil
export const fetchUsers = async () => {
  try {
    // Buscar perfis do armazenamento local
    const { data: profiles, error: profilesError } = await LocalStorageDB.findAll<Profile>(LocalStorageDB.STORAGE_KEYS.PROFILES);

    if (profilesError) throw profilesError;

    // Agora buscamos os emails dos usuários
    const emailMap = await getUserEmails();

    // Combinar dados do perfil com emails e garantir que o role seja do tipo UserRole
    const users: User[] = profiles.map(profile => ({
      ...profile,
      email: emailMap[profile.id] || `${profile.id.substring(0, 8)}@demo.com`,
      role: profile.role as UserRole // Aqui garantimos que o role seja tratado como UserRole
    }));

    return users;
  } catch (error) {
    console.error("Erro ao buscar usuários:", error);
    throw error;
  }
};

// Buscar um usuário específico
export const fetchUserById = async (userId: string) => {
  const { data, error } = await LocalStorageDB.findById<Profile>(LocalStorageDB.STORAGE_KEYS.PROFILES, userId);

  if (error) throw error;

  if (!data) {
    throw new Error('Usuário não encontrado');
  }

  // Obter o email do usuário
  const emailMap = await getUserEmails();
  return {
    ...data,
    email: emailMap[data.id] || `${data.id.substring(0, 8)}@demo.com`
  };
};

// Criar um novo usuário (simulado na versão demo)
export const createUser = async (userData: {
  email: string;
  password: string;
  name: string;
  phone: string;
  role: UserRole;
  active: boolean;
}) => {
  try {
    // Na versão demo, criamos diretamente no localStorage
    const newProfile: Omit<Profile, 'id' | 'created_at' | 'updated_at'> = {
      name: userData.name,
      role: userData.role,
      phone: userData.phone,
      active: userData.active
    };

    const { data, error } = await LocalStorageDB.create<Profile>(
      LocalStorageDB.STORAGE_KEYS.PROFILES,
      newProfile
    );

    if (error) throw error;

    return {
      ...data,
      email: userData.email
    };
  } catch (error) {
    console.error("Erro ao criar usuário:", error);
    throw error;
  }
};

// Atualizar um usuário existente
export const updateUser = async (userId: string, userData: {
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  active: boolean;
}) => {
  try {
    // Verificar se o usuário tinha status "ativo" anteriormente
    const { data: previousData, error: prevError } = await LocalStorageDB.findById<Profile>(
      LocalStorageDB.STORAGE_KEYS.PROFILES,
      userId
    );

    if (prevError) throw prevError;
    if (!previousData) throw new Error('Usuário não encontrado');

    // Na versão demo, não precisamos atualizar o email na autenticação
    // Apenas atualizamos o perfil no localStorage

    const { data: profileData, error: profileError } = await LocalStorageDB.update<Profile>(
      LocalStorageDB.STORAGE_KEYS.PROFILES,
      userId,
      {
        name: userData.name,
        phone: userData.phone,
        role: userData.role,
        active: userData.active
      }
    );

    if (profileError) throw profileError;

    // Se o usuário foi bloqueado (está sendo desativado), deslogar o usuário atual se for o mesmo
    if (previousData.active && !userData.active) {
      // Verificar se é o usuário atual
      const currentUser = LocalStorageDB.getCurrentUser();
      if (currentUser && currentUser.id === userId) {
        await localSupabase.auth.signOut();
      }
    }

    return {
      ...profileData,
      email: userData.email
    };
  } catch (error) {
    console.error("Erro ao atualizar usuário:", error);
    throw error;
  }
};

// Atualizar a senha de um usuário (simulado na versão demo)
export const updateUserPassword = async (userId: string, newPassword: string) => {
  try {
    // Na versão demo, apenas simulamos a atualização de senha
    // Não há armazenamento real de senhas

    // Verificar se o usuário existe
    const { data, error } = await LocalStorageDB.findById<Profile>(
      LocalStorageDB.STORAGE_KEYS.PROFILES,
      userId
    );

    if (error) throw error;
    if (!data) throw new Error('Usuário não encontrado');

    // Simular sucesso
    return { success: true };
  } catch (error) {
    console.error("Erro ao atualizar senha:", error);
    throw error;
  }
};

// Desativar ou reativar um usuário
export const toggleUserActive = async (userId: string, active: boolean) => {
  try {
    // Verificar se o usuário tinha status "ativo" anteriormente
    const { data: previousData, error: prevError } = await supabase
      .from('profiles')
      .select('active')
      .eq('id', userId)
      .single();
    
    if (prevError) throw prevError;
    
    // Atualizar o status de ativo/inativo
    const { data, error } = await supabase
      .from('profiles')
      .update({ active })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    
    // Limpar cache de admin se o usuário for o mesmo
    if (isAdminCacheUserId === userId) {
      isAdminCache = null;
      isAdminCacheUserId = null;
      isAdminCacheTimestamp = 0;
    }
    
    // Se o usuário foi bloqueado (está sendo desativado), deslogar o usuário
    if (previousData.active && !active) {
      // Chamar a função que invalida sessões do usuário
      const { error: invalidateError } = await supabase.functions.invoke('manage_users', {
        body: {
          action: 'invalidateUserSessions',
          userId
        }
      });
      
      if (invalidateError) {
        console.error("Erro ao invalidar sessões do usuário:", invalidateError);
        // Não interromper o fluxo em caso de erro aqui
      }
    }
    
    return data;
  } catch (error) {
    console.error("Erro ao atualizar status do usuário:", error);
    throw error;
  }
};

// Verificar se ainda há administradores ativos antes de deletar um admin
export const checkAdminsBeforeDelete = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('id, role, active')
    .eq('role', 'admin')
    .eq('active', true)
    .neq('id', userId);

  if (error) throw error;
  return data.length > 0; // Retorna true se houver pelo menos outro admin ativo
};

// Deletar um usuário
export const deleteUser = async (userId: string) => {
  try {
    // Deletar o usuário usando a Edge Function
    const { data, error } = await supabase.functions.invoke('manage_users', {
      body: {
        action: 'deleteUser',
        userId
      }
    });
    
    if (error) throw error;
    
    // Limpar caches
    clearEmailCache();
    
    // Limpar cache de admin se o usuário for o mesmo
    if (isAdminCacheUserId === userId) {
      isAdminCache = null;
      isAdminCacheUserId = null;
      isAdminCacheTimestamp = 0;
    }
    
    return true;
  } catch (error) {
    console.error("Erro ao deletar usuário:", error);
    throw error;
  }
};

// Verificar se o usuário atual é admin
export const isUserAdmin = async () => {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) return false;
  
  const userId = session.user.id;
  const now = Date.now();
  
  // Verificar se temos cache válido para este usuário
  if (isAdminCache !== null && 
      isAdminCacheUserId === userId && 
      now - isAdminCacheTimestamp < ADMIN_CACHE_TTL) {
    return isAdminCache;
  }
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error || !data) return false;
    
    // Atualizar cache
    isAdminCache = data.role === 'admin';
    isAdminCacheUserId = userId;
    isAdminCacheTimestamp = now;
    
    return isAdminCache;
  } catch (error) {
    console.error("Erro ao verificar permissões de admin:", error);
    return false;
  }
};
