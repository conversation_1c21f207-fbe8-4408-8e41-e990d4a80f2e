
import { supabase } from "@/integrations/supabase/client";
import { User, UserRole } from "@/pages/Users";

// Cache para armazenar emails temporariamente
let emailCache: Record<string, string> = {};
let emailCacheTimestamp = 0;
const EMAIL_CACHE_TTL = 5 * 60 * 1000; // 5 minutos

// Cache para verificação de admin
let isAdminCache: boolean | null = null;
let isAdminCacheUserId: string | null = null;
let isAdminCacheTimestamp = 0;
const ADMIN_CACHE_TTL = 10 * 60 * 1000; // 10 minutos

// Função para obter emails dos usuários com cache
async function getUserEmails() {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) return {};
    
    // Verificar se temos um cache válido
    const now = Date.now();
    if (Object.keys(emailCache).length > 0 && now - emailCacheTimestamp < EMAIL_CACHE_TTL) {
      return emailCache;
    }
    
    // Tentar buscar emails pela função Edge
    try {
      const { data, error } = await supabase.functions.invoke('get_user_emails');
      
      if (error) {
        console.error("Erro ao buscar emails:", error);
        throw error; // Lançar erro para cair no fallback
      }
      
      // Mapear os emails por ID para fácil acesso
      const emailMap: Record<string, string> = {};
      data.data.forEach((user: {id: string, email: string}) => {
        emailMap[user.id] = user.email;
      });
      
      // Atualizar cache
      emailCache = emailMap;
      emailCacheTimestamp = now;
      
      return emailMap;
    } catch (edgeFunctionError) {
      console.warn("Fallback: usando emails mascarados devido a um erro na função Edge:", edgeFunctionError);
      // Se a função Edge falhar, continuamos com emails mascarados
      return {};
    }
  } catch (error) {
    console.error("Erro ao buscar emails dos usuários:", error);
    return {};
  }
}

// Função para limpar o cache (útil após operações de mutação)
export const clearEmailCache = () => {
  emailCache = {};
  emailCacheTimestamp = 0;
};

// Buscar todos os usuários do perfil
export const fetchUsers = async () => {
  try {
    // Primeiro, buscamos os perfis ordenados por data de criação (mais recentes primeiro)
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (profilesError) throw profilesError;
    
    // Agora buscamos os emails dos usuários
    const emailMap = await getUserEmails();
    
    // Combinar dados do perfil com emails e garantir que o role seja do tipo UserRole
    const users: User[] = profiles.map(profile => ({
      ...profile,
      email: emailMap[profile.id] || `${profile.id.substring(0, 8)}@oluchys.com`,
      role: profile.role as UserRole // Aqui garantimos que o role seja tratado como UserRole
    }));
    
    return users;
  } catch (error) {
    console.error("Erro ao buscar usuários:", error);
    throw error;
  }
};

// Buscar um usuário específico
export const fetchUserById = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) throw error;
  
  // Obter o email do usuário
  const emailMap = await getUserEmails();
  return {
    ...data,
    email: emailMap[data.id] || `${data.id.substring(0, 8)}@oluchys.com`
  };
};

// Criar um novo usuário
export const createUser = async (userData: { 
  email: string; 
  password: string; 
  name: string; 
  phone: string; 
  role: UserRole;
  active: boolean;
}) => {
  try {
    // Invocar a função Edge para criar o usuário
    const { data, error } = await supabase.functions.invoke('manage_users', {
      body: {
        action: 'createUser',
        email: userData.email,
        password: userData.password,
        userData: {
          name: userData.name,
          role: userData.role,
          phone: userData.phone,
          active: userData.active
        }
      }
    });
    
    if (error) throw error;
    
    // Limpar cache após criar usuário
    clearEmailCache();
    
    return data;
  } catch (error) {
    console.error("Erro ao criar usuário:", error);
    throw error;
  }
};

// Atualizar um usuário existente
export const updateUser = async (userId: string, userData: {
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  active: boolean;
}) => {
  try {
    // Verificar se o usuário tinha status "ativo" anteriormente
    const { data: previousData, error: prevError } = await supabase
      .from('profiles')
      .select('active')
      .eq('id', userId)
      .single();
    
    if (prevError) throw prevError;
    
    // 1. Atualizar o email na autenticação se necessário
    const { data: authData, error: authError } = await supabase.functions.invoke('manage_users', {
      body: {
        action: 'updateUserEmail',
        userId,
        email: userData.email
      }
    });

    if (authError) throw authError;

    // 2. Atualizar informações no perfil
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .update({
        name: userData.name,
        phone: userData.phone,
        role: userData.role,
        active: userData.active
      })
      .eq('id', userId)
      .select()
      .single();

    if (profileError) throw profileError;
    
    // 3. Se o usuário foi bloqueado (está sendo desativado), deslogar o usuário
    if (previousData.active && !userData.active) {
      // Chamar a função que invalida sessões do usuário
      const { error: invalidateError } = await supabase.functions.invoke('manage_users', {
        body: {
          action: 'invalidateUserSessions',
          userId
        }
      });
      
      if (invalidateError) {
        console.error("Erro ao invalidar sessões do usuário:", invalidateError);
        // Não interromper o fluxo em caso de erro aqui
      }
    }
    
    // Limpar cache após atualizar usuário
    clearEmailCache();
    
    // Limpar cache de admin se o usuário for o mesmo ou se mudou de cargo
    if (isAdminCacheUserId === userId || userData.role === 'admin') {
      isAdminCache = null;
      isAdminCacheUserId = null;
      isAdminCacheTimestamp = 0;
    }
    
    return {
      ...profileData,
      email: userData.email
    };
  } catch (error) {
    console.error("Erro ao atualizar usuário:", error);
    throw error;
  }
};

// Atualizar a senha de um usuário
export const updateUserPassword = async (userId: string, newPassword: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('manage_users', {
      body: {
        action: 'updateUserPassword',
        userId,
        password: newPassword
      }
    });
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Erro ao atualizar senha:", error);
    throw error;
  }
};

// Desativar ou reativar um usuário
export const toggleUserActive = async (userId: string, active: boolean) => {
  try {
    // Verificar se o usuário tinha status "ativo" anteriormente
    const { data: previousData, error: prevError } = await supabase
      .from('profiles')
      .select('active')
      .eq('id', userId)
      .single();
    
    if (prevError) throw prevError;
    
    // Atualizar o status de ativo/inativo
    const { data, error } = await supabase
      .from('profiles')
      .update({ active })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    
    // Limpar cache de admin se o usuário for o mesmo
    if (isAdminCacheUserId === userId) {
      isAdminCache = null;
      isAdminCacheUserId = null;
      isAdminCacheTimestamp = 0;
    }
    
    // Se o usuário foi bloqueado (está sendo desativado), deslogar o usuário
    if (previousData.active && !active) {
      // Chamar a função que invalida sessões do usuário
      const { error: invalidateError } = await supabase.functions.invoke('manage_users', {
        body: {
          action: 'invalidateUserSessions',
          userId
        }
      });
      
      if (invalidateError) {
        console.error("Erro ao invalidar sessões do usuário:", invalidateError);
        // Não interromper o fluxo em caso de erro aqui
      }
    }
    
    return data;
  } catch (error) {
    console.error("Erro ao atualizar status do usuário:", error);
    throw error;
  }
};

// Verificar se ainda há administradores ativos antes de deletar um admin
export const checkAdminsBeforeDelete = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('id, role, active')
    .eq('role', 'admin')
    .eq('active', true)
    .neq('id', userId);

  if (error) throw error;
  return data.length > 0; // Retorna true se houver pelo menos outro admin ativo
};

// Deletar um usuário
export const deleteUser = async (userId: string) => {
  try {
    // Deletar o usuário usando a Edge Function
    const { data, error } = await supabase.functions.invoke('manage_users', {
      body: {
        action: 'deleteUser',
        userId
      }
    });
    
    if (error) throw error;
    
    // Limpar caches
    clearEmailCache();
    
    // Limpar cache de admin se o usuário for o mesmo
    if (isAdminCacheUserId === userId) {
      isAdminCache = null;
      isAdminCacheUserId = null;
      isAdminCacheTimestamp = 0;
    }
    
    return true;
  } catch (error) {
    console.error("Erro ao deletar usuário:", error);
    throw error;
  }
};

// Verificar se o usuário atual é admin
export const isUserAdmin = async () => {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) return false;
  
  const userId = session.user.id;
  const now = Date.now();
  
  // Verificar se temos cache válido para este usuário
  if (isAdminCache !== null && 
      isAdminCacheUserId === userId && 
      now - isAdminCacheTimestamp < ADMIN_CACHE_TTL) {
    return isAdminCache;
  }
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error || !data) return false;
    
    // Atualizar cache
    isAdminCache = data.role === 'admin';
    isAdminCacheUserId = userId;
    isAdminCacheTimestamp = now;
    
    return isAdminCache;
  } catch (error) {
    console.error("Erro ao verificar permissões de admin:", error);
    return false;
  }
};
