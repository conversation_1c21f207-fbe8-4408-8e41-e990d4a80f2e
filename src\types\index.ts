
export interface Transaction {
  id: string;
  amount: number;
  type: 'entrada' | 'saida';
  description: string;
  category?: string;
  sale_id?: string;
  payment_method_id?: string;
  payment_method?: {
    id: string;
    name: string;
  };
  user_id: string;
  transaction_date: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
}

export interface Notification {
  id: string;
  message: string;
  type: string;
  read: boolean;
  user_id?: string;
  reference_id?: string;
  reference_type?: string;
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  name: string;
  phone?: string;
  role: 'admin' | 'vendedor';
  active: boolean;
  avatar_url?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface DeliveryPerson {
  id: string;
  name: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  id: string;
  name: string;
  category_id: string;
  price: number;
  category?: {
    id: string;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface ProductVariant {
  id: string;
  product_id: string;
  color_id: string;
  size_id: string;
  quantity: number;
  product?: Product;
  color?: {
    id: string;
    name: string;
    hex_code?: string;
  };
  size?: {
    id: string;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface Sale {
  id: string;
  sale_number: number;
  customer_name: string;
  customer_contact?: string;
  product_variant_id: string;
  price: number;
  payment_method_id: string;
  payment_proof?: string;
  notes?: string;
  seller_id: string;
  delivery_person_id?: string;
  status_id: string;
  sale_date: string;
  created_at: string;
  updated_at: string;
  product_variant?: ProductVariant;
  payment_method?: PaymentMethod;
  status?: {
    id: string;
    name: string;
  };
  seller?: Profile;
  delivery_person?: DeliveryPerson;
}

export interface ProductCategory {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Color {
  id: string;
  name: string;
  hex_code?: string;
  created_at: string;
  updated_at: string;
}

export interface Size {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface SaleStatus {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}
