
project_id = "ffirmjpvmughvtiawewp"

[api]
port = 54321
schemas = ["public", "storage"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[studio]
port = 54323

[inbucket]
port = 54324
smtp_port = 54325
pop3_port = 54326

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10

[realtime]
enabled = true

[storage]
enabled = true
file_size_limit = "50MiB"

[functions.delete_user_by_email]
verify_jwt = false

[functions.get_user_emails]
verify_jwt = false

[functions.manage_users]
verify_jwt = false
