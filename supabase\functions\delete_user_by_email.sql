
-- Função para deletar usuário pelo email
CREATE OR REPLACE FUNCTION public.delete_user_by_email(email_to_delete TEXT)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id uuid;
BEGIN
  -- Get user ID from auth.users
  SELECT id INTO user_id
  FROM auth.users
  WHERE email = email_to_delete;
  
  -- Delete from auth.users if user exists
  IF user_id IS NOT NULL THEN
    -- Delete from profiles first (if it exists)
    DELETE FROM public.profiles WHERE id = user_id;
    
    -- Delete from auth.users
    DELETE FROM auth.users WHERE id = user_id;
  END IF;
END;
$$;
