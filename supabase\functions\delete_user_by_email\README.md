
# Delete User by Email Edge Function

This edge function allows for deleting a user by their email address. It first removes any related profile data and then deletes the user authentication record.

## Usage

Call this function with a POST request containing a JSON body with an `email` field:

```json
{
  "email": "<EMAIL>"
}
```

## Permissions

This function requires the Supabase service role key to work correctly, as it uses admin APIs to delete users.

## Error Handling

The function will return appropriate HTTP status codes:
- 400: If email is not provided
- 404: If user is not found
- 500: If there was an error during deletion
- 200: If deletion was successful
