
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Criar um cliente Supabase com a chave de admin
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Obter o token de autenticação do usuário
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Não autorizado. Token não fornecido.' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Verificar se o usuário é um administrador
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Usuário não autenticado.' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Verificar se o usuário é um administrador
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
      
    if (profileError || profile?.role !== 'admin') {
      return new Response(
        JSON.stringify({ error: 'Permissão negada. Apenas administradores podem executar esta operação.' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Processar a requisição
    const { action, ...params } = await req.json();

    switch (action) {
      case 'createUser': {
        const { email, password, userData } = params;
        
        // Criar o usuário
        const { data, error } = await supabaseAdmin.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: userData
        });
        
        if (error) throw error;
        
        // Atualizar o perfil manualmente para garantir que todos os campos sejam preenchidos
        if (data.user) {
          const { error: profileError } = await supabaseAdmin
            .from('profiles')
            .update({
              name: userData.name,
              phone: userData.phone || null,
              role: userData.role,
              active: userData.active !== undefined ? userData.active : true
            })
            .eq('id', data.user.id);
            
          if (profileError) {
            console.error("Erro ao atualizar perfil:", profileError);
            // Não lançar erro aqui para não reverter a criação do usuário
          }
        }
        
        return new Response(
          JSON.stringify({ success: true, data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      case 'updateUserEmail': {
        const { userId, email } = params;
        
        const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
          userId,
          { email }
        );
        
        if (error) throw error;
        return new Response(
          JSON.stringify({ success: true, data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      case 'updateUserPassword': {
        const { userId, password } = params;
        
        const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
          userId,
          { password }
        );
        
        if (error) throw error;
        return new Response(
          JSON.stringify({ success: true, data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      case 'invalidateUserSessions': {
        const { userId } = params;
        
        // Invalidar todas as sessões do usuário forçando ele a sair do sistema
        const { error } = await supabaseAdmin.auth.admin.signOut(userId);
        
        if (error) throw error;
        return new Response(
          JSON.stringify({ success: true, message: "Sessões do usuário invalidadas com sucesso" }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      case 'deleteUser': {
        const { userId } = params;
        
        // Verificar se o usuário a ser excluído é um administrador
        const { data: userProfile, error: userProfileError } = await supabaseAdmin
          .from('profiles')
          .select('role')
          .eq('id', userId)
          .single();
          
        if (userProfileError) throw userProfileError;
        
        // Se for admin, verificar se há outros admins ativos
        if (userProfile?.role === 'admin') {
          const { data: admins, error: adminsError } = await supabaseAdmin
            .from('profiles')
            .select('id')
            .eq('role', 'admin')
            .eq('active', true)
            .neq('id', userId);
            
          if (adminsError) throw adminsError;
          
          if (admins.length === 0) {
            return new Response(
              JSON.stringify({ 
                error: 'Não é possível excluir o último administrador ativo.' 
              }),
              { 
                status: 400, 
                headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
              }
            );
          }
        }
        
        // Excluir o usuário
        const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
        
        if (error) throw error;
        return new Response(
          JSON.stringify({ success: true }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      default:
        return new Response(
          JSON.stringify({ error: 'Ação não suportada.' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }
  } catch (error) {
    console.error('Erro no sistema de gerenciamento de usuários:', error);
    
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Ocorreu um erro desconhecido',
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
